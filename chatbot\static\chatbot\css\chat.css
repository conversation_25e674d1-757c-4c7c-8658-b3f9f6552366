/* Modern Custom styles for the chatbot interface */

/* Enhanced Chat messages styling */
.chat-message {
    animation: slideInMessage 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideInMessage {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Glassmorphism effect */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Enhanced floating animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-15px);
    }
}

/* Pulse glow effect */
@keyframes pulse-glow {
    0% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
    }
    50% {
        box-shadow: 0 0 40px rgba(59, 130, 246, 0.8), 0 0 60px rgba(147, 51, 234, 0.4);
    }
    100% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
    }
}

/* Enhanced User message styling */
.user-message {
    margin-left: auto;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%, #f093fb 200%);
    color: white;
    border-radius: 24px 24px 8px 24px;
    max-width: 75%;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    transform: translateX(0);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.user-message:hover {
    transform: translateX(-5px) translateY(-2px);
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
}

/* Enhanced Bot message styling */
.bot-message {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid rgba(59, 130, 246, 0.1);
    border-radius: 24px 24px 24px 8px;
    max-width: 75%;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    transform: translateX(0);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
}

.bot-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bot-message:hover {
    transform: translateX(5px) translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    border-color: rgba(59, 130, 246, 0.2);
}

.bot-message:hover::before {
    opacity: 1;
}

/* Message timestamp */
.message-timestamp {
    font-size: 0.75rem;
    color: #9ca3af;
    margin-top: 0.5rem;
    display: block;
}

/* Confidence indicator */
.confidence-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 0.5rem;
}

.confidence-high {
    background-color: #10b981;
}

.confidence-medium {
    background-color: #f59e0b;
}

.confidence-low {
    background-color: #ef4444;
}

/* Typing indicator animation */
@keyframes bounce {
    0%, 60%, 100% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(-10px);
    }
}

.typing-dot {
    animation: bounce 1.4s infinite;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

/* Chat input focus effects */
#message-input:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    border-color: #3b82f6;
}

/* Send button hover effects */
#send-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

#send-button:active {
    transform: translateY(0);
}

/* Quick question buttons */
.quick-question:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Scrollbar styling for chat messages */
#chat-messages::-webkit-scrollbar {
    width: 6px;
}

#chat-messages::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

#chat-messages::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

#chat-messages::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* RTL support for Arabic text */
.rtl {
    direction: rtl;
    text-align: right;
}

.ltr {
    direction: ltr;
    text-align: left;
}

/* Message feedback buttons */
.feedback-buttons {
    margin-top: 0.5rem;
    display: flex;
    gap: 0.5rem;
}

.feedback-btn {
    background: none;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s;
}

.feedback-btn:hover {
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

.feedback-btn.active {
    background-color: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error message styling */
.error-message {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}

/* Success message styling */
.success-message {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #166534;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .user-message,
    .bot-message {
        max-width: 85%;
    }

    #chat-messages {
        height: 300px;
    }

    .quick-question {
        font-size: 0.875rem;
        padding: 0.75rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .bot-message {
        background-color: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }

    .message-timestamp {
        color: #9ca3af;
    }

    .quick-question {
        background-color: #374151;
        color: #f9fafb;
    }

    .quick-question:hover {
        background-color: #4b5563;
    }
}
