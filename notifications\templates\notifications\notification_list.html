{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Notifications - <PERSON><PERSON><PERSON>{% endblock %}

{% block body %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Notification Actions -->
    <div class="flex justify-between items-center mb-6">
        <div class="flex items-center">
            <span class="text-lg font-medium text-gray-900 mr-2">
                {% if unread_count %}
                    {{ unread_count }} Unread
                {% else %}
                    All Notifications
                {% endif %}
            </span>
            {% if unread_count %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                    {{ unread_count }}
                </span>
            {% endif %}
        </div>
        <div class="flex space-x-2">
            <a href="{% url 'notification_preferences' %}" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
                <i class="fas fa-cog mr-2"></i> Preferences
            </a>
            {% if unread_count %}
                <form action="{% url 'mark_all_notifications_read' %}" method="post" class="inline">
                    {% csrf_token %}
                    <button type="submit" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
                        <i class="fas fa-check-double mr-2"></i> Mark All Read
                    </button>
                </form>
            {% endif %}
        </div>
    </div>

    <!-- Notifications List -->
    <div class="bg-white shadow-sm rounded-lg overflow-hidden">
        {% if page_obj %}
            <ul class="divide-y divide-gray-200">
                {% for notification in page_obj %}
                    <li class="{% if not notification.is_read %}bg-blue-50{% endif %} hover:bg-gray-50 transition-colors duration-300">
                        <div class="px-4 py-4 sm:px-6 relative">
                            <!-- Notification Actions -->
                            <div class="absolute top-4 right-4 flex space-x-2">
                                {% if notification.is_read %}
                                    <form action="{% url 'mark_notification_unread' notification.id %}" method="post" class="inline">
                                        {% csrf_token %}
                                        <button type="submit" class="text-gray-400 hover:text-gray-500" title="Mark as unread">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </form>
                                {% else %}
                                    <form action="{% url 'mark_notification_read' notification.id %}" method="post" class="inline">
                                        {% csrf_token %}
                                        <button type="submit" class="text-gray-400 hover:text-gray-500" title="Mark as read">
                                            <i class="fas fa-envelope-open"></i>
                                        </button>
                                    </form>
                                {% endif %}
                                <form action="{% url 'delete_notification' notification.id %}" method="post" class="inline">
                                    {% csrf_token %}
                                    <button type="submit" class="text-gray-400 hover:text-red-500" title="Delete notification">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </form>
                            </div>
                            
                            <!-- Notification Content -->
                            <a href="{% url 'notification_detail' notification.id %}" class="block">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="h-10 w-10 rounded-full flex items-center justify-center {{ notification.bg_color_class }}">
                                            <i class="fas {{ notification.icon_class }} {{ notification.color_class }}"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4 flex-1">
                                        <div class="flex justify-between">
                                            <p class="text-sm font-medium text-gray-900">{{ notification.title }}</p>
                                            <p class="text-xs text-gray-500">{{ notification.time_since }}</p>
                                        </div>
                                        <p class="text-sm text-gray-500 mt-1">{{ notification.message }}</p>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </li>
                {% endfor %}
            </ul>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing
                                <span class="font-medium">{{ page_obj.start_index }}</span>
                                to
                                <span class="font-medium">{{ page_obj.end_index }}</span>
                                of
                                <span class="font-medium">{{ page_obj.paginator.count }}</span>
                                results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                {% else %}
                                    <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                        <span class="sr-only">Previous</span>
                                        <i class="fas fa-chevron-left"></i>
                                    </span>
                                {% endif %}
                                
                                {% for i in page_obj.paginator.page_range %}
                                    {% if page_obj.number == i %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-primary-500 bg-primary-50 text-sm font-medium text-primary-600">
                                            {{ i }}
                                        </span>
                                    {% else %}
                                        <a href="?page={{ i }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            {{ i }}
                                        </a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                {% else %}
                                    <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                        <span class="sr-only">Next</span>
                                        <i class="fas fa-chevron-right"></i>
                                    </span>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <div class="mx-auto h-24 w-24 text-gray-400">
                    <i class="fas fa-bell-slash text-6xl"></i>
                </div>
                <h3 class="mt-2 text-lg font-medium text-gray-900">No notifications</h3>
                <p class="mt-1 text-gray-500">You don't have any notifications yet.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
