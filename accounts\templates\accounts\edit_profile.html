{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Edit Profile - Mazadi{% endblock %}

{% block body %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Sidebar Navigation -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden sticky top-24">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        {% if profile.profile_picture %}
                            <img src="{{ profile.profile_picture.url }}" alt="{{ user.username }}" class="h-16 w-16 rounded-full object-cover border-2 border-primary-500">
                        {% else %}
                            <div class="h-16 w-16 rounded-full bg-primary-100 flex items-center justify-center border-2 border-primary-500">
                                <i class="fas fa-user text-2xl text-primary-500"></i>
                            </div>
                        {% endif %}
                        <div class="ml-4">
                            <h2 class="text-lg font-semibold text-gray-900">{{ user.username }}</h2>
                            <p class="text-sm text-gray-500">{{ user.email }}</p>
                        </div>
                    </div>

                    <nav class="space-y-2">
                        <a href="{% url 'profile' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md bg-primary-50 text-primary-700">
                            <i class="fas fa-user-circle mr-3 text-primary-500"></i>
                            Profile Overview
                        </a>
                        <a href="{% url 'edit_profile' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-edit mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            Edit Profile
                        </a>
                        <a href="{% url 'change_password' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-key mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            Change Password
                        </a>
                        <a href="{% url 'security_questions' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-shield-alt mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            Security Questions
                        </a>
                        <a href="{% url 'my_auctions' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-gavel mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            My Auctions
                        </a>
                        <a href="{% url 'watchlist' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-heart mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            Watchlist
                        </a>

                        <div class="border-t border-gray-200 my-2"></div>

                        <a href="{% url 'payment_history' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-credit-card mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            My Payments
                        </a>
                        <a href="{% url 'seller_payments' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-dollar-sign mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            Seller Payments
                        </a>
                        <a href="{% url 'messages_inbox' %}" class="flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-primary-700 transition-colors duration-300">
                            <i class="fas fa-comments mr-3 text-gray-400 group-hover:text-primary-500"></i>
                            Messages
                        </a>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="lg:col-span-3">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-6">Edit Profile</h2>

                    <form method="post" enctype="multipart/form-data" class="space-y-6">
                        {% csrf_token %}

                        <!-- Profile Picture -->
                        <div class="mb-8">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Profile Picture</label>
                            <div class="flex items-center">
                                <div class="mr-4">
                                    {% if profile.profile_picture %}
                                        <img src="{{ profile.profile_picture.url }}" alt="{{ user.username }}" class="h-24 w-24 rounded-full object-cover border-2 border-primary-500">
                                    {% else %}
                                        <div class="h-24 w-24 rounded-full bg-primary-100 flex items-center justify-center border-2 border-primary-500">
                                            <i class="fas fa-user text-3xl text-primary-500"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div>
                                    <label for="id_profile_picture" class="cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-300">
                                        <i class="fas fa-upload mr-2"></i> Upload New Picture
                                    </label>
                                    {{ form.profile_picture }}
                                    <p class="mt-1 text-xs text-gray-500">Recommended: Square image, at least 200x200 pixels</p>
                                </div>
                            </div>
                        </div>

                        <!-- Personal Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="id_first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-user text-gray-400"></i>
                                        </div>
                                        {{ form.first_name }}
                                    </div>
                                </div>
                                <div>
                                    <label for="id_last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-user text-gray-400"></i>
                                        </div>
                                        {{ form.last_name }}
                                    </div>
                                </div>
                                <div class="md:col-span-2">
                                    <label for="id_email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-envelope text-gray-400"></i>
                                        </div>
                                        {{ form.email }}
                                    </div>
                                </div>
                                <div class="md:col-span-2">
                                    <label for="id_bio" class="block text-sm font-medium text-gray-700 mb-1">Bio</label>
                                    {{ form.bio }}
                                    <p class="mt-1 text-xs text-gray-500">Tell other users about yourself</p>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="md:col-span-2">
                                    <label for="id_phone_number" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-phone text-gray-400"></i>
                                        </div>
                                        {{ form.phone_number }}
                                    </div>
                                </div>
                                <div class="md:col-span-2">
                                    <label for="id_address_line1" class="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-home text-gray-400"></i>
                                        </div>
                                        {{ form.address_line1 }}
                                    </div>
                                </div>
                                <div class="md:col-span-2">
                                    <label for="id_address_line2" class="block text-sm font-medium text-gray-700 mb-1">Address Line 2</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-building text-gray-400"></i>
                                        </div>
                                        {{ form.address_line2 }}
                                    </div>
                                </div>
                                <div>
                                    <label for="id_city" class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-city text-gray-400"></i>
                                        </div>
                                        {{ form.city }}
                                    </div>
                                </div>
                                <div>
                                    <label for="id_state" class="block text-sm font-medium text-gray-700 mb-1">State/Province</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-map-marker-alt text-gray-400"></i>
                                        </div>
                                        {{ form.state }}
                                    </div>
                                </div>
                                <div>
                                    <label for="id_postal_code" class="block text-sm font-medium text-gray-700 mb-1">Postal/Zip Code</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-mail-bulk text-gray-400"></i>
                                        </div>
                                        {{ form.postal_code }}
                                    </div>
                                </div>
                                <div>
                                    <label for="id_country" class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-globe text-gray-400"></i>
                                        </div>
                                        {{ form.country }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-300">
                                <i class="fas fa-save mr-2"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Preview uploaded profile picture
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('id_profile_picture');

        if (fileInput) {
            fileInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        const profilePicture = document.querySelector('.h-24.w-24');

                        if (profilePicture.tagName === 'IMG') {
                            profilePicture.src = e.target.result;
                        } else {
                            // Create new image if it doesn't exist
                            const newImg = document.createElement('img');
                            newImg.src = e.target.result;
                            newImg.alt = "Profile Picture";
                            newImg.className = "h-24 w-24 rounded-full object-cover border-2 border-primary-500";

                            profilePicture.parentNode.replaceChild(newImg, profilePicture);
                        }
                    }

                    reader.readAsDataURL(this.files[0]);
                }
            });
        }
    });
</script>
{% endblock %}
