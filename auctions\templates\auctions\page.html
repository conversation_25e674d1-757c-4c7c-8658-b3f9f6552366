{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}{{ category }} - <PERSON><PERSON><PERSON>{% endblock %}

{% block extra_head %}
<script>
    // Add hover animation for cards
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.animate-slide-in');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.zIndex = '10';
            });
            card.addEventListener('mouseleave', function() {
                this.style.zIndex = '1';
            });
        });
    });
</script>
{% endblock %}

{% block body %}
<!-- Breadcrumb -->
<div class="bg-gray-100 py-4 border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <div>
                        <a href="{% url 'home' %}" class="text-gray-500 hover:text-gray-700 transition duration-300">
                            <i class="fas fa-home"></i>
                            <span class="sr-only">Home</span>
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                        <a href="{% url 'categories' %}" class="ml-4 text-gray-500 hover:text-gray-700 transition duration-300">Categories</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                        <span class="ml-4 text-gray-700 font-medium">{{ category }}</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>
</div>

<!-- Page Header -->
<div class="bg-gradient-to-r from-primary-600 to-primary-800 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-3xl font-extrabold text-white sm:text-4xl">
                {{ category }}
            </h1>
            <p class="mt-3 max-w-2xl mx-auto text-xl text-primary-100">
                Browse all {{ auction_count }} auctions in the {{ category }} category
            </p>
        </div>
    </div>
</div>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Auctions Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        {% for auction in auctions %}
        <div class="animate-slide-in" style="animation-delay: {{ forloop.counter0 }}00ms">
            <div class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl h-full flex flex-col group">
                <!-- Image container with enhanced hover effects -->
                <div class="h-64 overflow-hidden relative">
                    <!-- Status badge -->
                    <div class="absolute top-3 right-3 z-10">
                        {% if auction.is_close %}
                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 shadow-sm transform transition-transform duration-300 group-hover:scale-110">
                                <i class="fas fa-times-circle mr-1"></i> Closed
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 shadow-sm transform transition-transform duration-300 group-hover:scale-110">
                                <i class="fas fa-check-circle mr-1"></i> Active
                            </span>
                        {% endif %}
                    </div>

                    <!-- Image with enhanced hover effect -->
                    {% if auction.image %}
                        <img
                            src="{{ auction.image.url }}"
                            class="w-full h-full object-contain transition-all duration-700 ease-in-out transform group-hover:scale-110 filter group-hover:brightness-105"
                            alt="{{ auction.title }}"
                            loading="lazy"
                        >
                    {% elif auction.image_url %}
                        <img
                            src="{{ auction.image_url }}"
                            class="w-full h-full object-contain transition-all duration-700 ease-in-out transform group-hover:scale-110 filter group-hover:brightness-105"
                            alt="{{ auction.title }}"
                            loading="lazy"
                        >
                    {% else %}
                        <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-image text-gray-400 text-4xl"></i>
                        </div>
                    {% endif %}

                    <!-- Overlay gradient on hover -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-0 group-hover:opacity-50 transition-opacity duration-300"></div>
                </div>

                <!-- Content with enhanced styling -->
                <div class="p-6 flex flex-col flex-grow">
                    <div class="mb-3">
                        <h3 class="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{ auction.title }}</h3>
                        <p class="text-gray-600 mt-2 line-clamp-2">{{ auction.description }}</p>
                    </div>

                    <div class="mt-auto">
                        <!-- Price with animation -->
                        <div class="flex justify-end mb-4">
                            <div class="bg-primary-50 px-3 py-1 rounded-full">
                                <span class="text-lg font-bold text-primary-700 group-hover:text-primary-800 transition-colors duration-300 inline-block transform group-hover:scale-110 transition-transform">
                                    EGP{{ auction.price }}
                                </span>
                            </div>
                        </div>

                        <!-- CTA Button with enhanced hover effect -->
                        <a href="{% url 'auction' auction.id %}" class="relative overflow-hidden block w-full text-center px-4 py-3 border-2 border-primary-600 text-primary-600 rounded-md bg-transparent group-hover:bg-primary-600 group-hover:text-white transition-all duration-300 group-hover:shadow-lg group-hover:border-primary-700 transform group-hover:scale-105">
                            <span class="relative z-10 flex items-center justify-center font-medium">
                                <span>View Details</span>
                                <i class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-1"></i>
                            </span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
            <div class="col-span-full animate-fade-in">
                <div class="bg-white border border-gray-200 p-8 rounded-lg shadow-md text-center max-w-2xl mx-auto">
                    <div class="mb-6">
                        <div class="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4 transform transition-transform duration-700 hover:rotate-12">
                            <i class="fas fa-tag text-yellow-500 text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">No Auctions in {{ category }}</h3>
                        <p class="text-gray-600 max-w-md mx-auto">
                            There are no auctions listed in the {{ category }} category yet. Be the first to create one!
                        </p>
                    </div>

                    {% if user.is_authenticated %}
                        <div class="mt-6">
                            <a href="{% url 'create' %}" class="inline-flex items-center justify-center bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-md shadow-md transition-all duration-300 transform hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <i class="fas fa-plus-circle mr-2"></i> Create {{ category }} Auction
                            </a>
                        </div>
                    {% else %}
                        <div class="mt-6 space-y-4">
                            <p class="text-gray-600">Sign in to create your first auction</p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                <a href="{% url 'login' %}" class="inline-flex items-center justify-center bg-white border border-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm transition-all duration-300 hover:bg-gray-50 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    <i class="fas fa-sign-in-alt mr-2"></i> Log In
                                </a>
                                <a href="{% url 'register' %}" class="inline-flex items-center justify-center bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md shadow-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    <i class="fas fa-user-plus mr-2"></i> Register
                                </a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Category Description -->
    <div class="bg-white rounded-lg shadow-sm p-6 mb-12">
        <h2 class="text-xl font-bold text-gray-900 mb-4">About {{ category }}</h2>
        <p class="text-gray-600 mb-4">
            Browse all items in our {{ category }} category. Find unique items from sellers around the world.
        </p>
        <div class="flex items-center text-primary-600">
            <a href="{% url 'categories' %}" class="inline-flex items-center font-medium hover:text-primary-700 transition duration-300">
                <i class="fas fa-arrow-left mr-2"></i> Back to All Categories
            </a>
        </div>
    </div>
</div>
{% endblock %}