# Generated by Django 5.2.1 on 2025-06-03 16:07

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auctions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PriceEstimationCache',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cache_key', models.CharField(db_index=True, help_text='Unique cache key for this estimation', max_length=255, unique=True)),
                ('estimated_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('amazon_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('dubizzle_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('sources_used', models.JSONField(blank=True, default=list)),
                ('estimation_errors', models.JSONField(blank=True, default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(help_text='When this cache entry expires')),
                ('hit_count', models.PositiveIntegerField(default=0, help_text='Number of times this cache entry was used')),
            ],
            options={
                'verbose_name': 'Price Estimation Cache',
                'verbose_name_plural': 'Price Estimation Cache Entries',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['cache_key'], name='price_estim_cache_k_8367fe_idx'), models.Index(fields=['expires_at'], name='price_estim_expires_4fd409_idx')],
            },
        ),
        migrations.CreateModel(
            name='PriceEstimation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('estimated_price', models.DecimalField(blank=True, decimal_places=2, help_text='Final estimated price calculated from all sources', max_digits=10, null=True)),
                ('amazon_price', models.DecimalField(blank=True, decimal_places=2, help_text='Price found on Amazon Egypt', max_digits=10, null=True)),
                ('dubizzle_price', models.DecimalField(blank=True, decimal_places=2, help_text='Price found on Dubizzle Egypt', max_digits=10, null=True)),
                ('sources_used', models.JSONField(blank=True, default=list, help_text='List of sources that provided price data')),
                ('estimation_errors', models.JSONField(blank=True, default=list, help_text='List of errors encountered during estimation')),
                ('estimation_metadata', models.JSONField(blank=True, default=dict, help_text='Additional metadata about the estimation process')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('auction', models.ForeignKey(help_text='The auction this price estimation belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='price_estimations', to='auctions.auction')),
                ('requested_by', models.ForeignKey(blank=True, help_text='User who requested this price estimation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='price_estimations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Price Estimation',
                'verbose_name_plural': 'Price Estimations',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['auction', '-created_at'], name='price_estim_auction_ae2394_idx'), models.Index(fields=['created_at'], name='price_estim_created_fdf664_idx')],
            },
        ),
    ]
