{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Register - Mazadi{% endblock %}

{% block body %}
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="bg-white rounded-lg shadow-md overflow-hidden animate-fade-in">
        <div class="p-8">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Register</h2>
                <p class="text-gray-600">Create your account to start using Mazadi</p>
            </div>

            <!-- Register Form -->
            <form action="{% url 'register' %}" method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Username Field -->
                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Username <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                        {{ form.username }}
                    </div>
                    {% if form.username.errors %}
                        <p class="mt-1 text-xs text-red-600">{{ form.username.errors.0 }}</p>
                    {% else %}
                        <p class="mt-1 text-xs text-gray-500">Your unique username for Mazadi</p>
                    {% endif %}
                </div>

                <!-- First Name Field -->
                <div>
                    <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        First Name <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                        {{ form.first_name }}
                    </div>
                    {% if form.first_name.errors %}
                        <p class="mt-1 text-xs text-red-600">{{ form.first_name.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Last Name Field -->
                <div>
                    <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Last Name <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                        {{ form.last_name }}
                    </div>
                    {% if form.last_name.errors %}
                        <p class="mt-1 text-xs text-red-600">{{ form.last_name.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Email Field -->
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Email <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                        {{ form.email }}
                    </div>
                    {% if form.email.errors %}
                        <p class="mt-1 text-xs text-red-600">{{ form.email.errors.0 }}</p>
                    {% else %}
                        <p class="mt-1 text-xs text-gray-500">We'll never share your email with anyone else</p>
                    {% endif %}
                </div>

                <!-- Password Field -->
                <div>
                    <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Password <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        {{ form.password1 }}
                    </div>
                    {% if form.password1.errors %}
                        <p class="mt-1 text-xs text-red-600">{{ form.password1.errors.0 }}</p>
                    {% else %}
                        <ul class="mt-1 text-xs text-gray-500 space-y-1 pl-5 list-disc">
                            <li>Your password must contain at least 8 characters.</li>
                            <li>Your password can't be too similar to your other personal information.</li>
                            <li>Your password can't be a commonly used password.</li>
                            <li>Your password can't be entirely numeric.</li>
                        </ul>
                    {% endif %}
                </div>

                <!-- Confirm Password Field -->
                <div>
                    <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Confirm Password <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        {{ form.password2 }}
                    </div>
                    {% if form.password2.errors %}
                        <p class="mt-1 text-xs text-red-600">{{ form.password2.errors.0 }}</p>
                    {% else %}
                        <p class="mt-1 text-xs text-gray-500">Enter the same password as before, for verification</p>
                    {% endif %}
                </div>

                <!-- Non-field errors -->
                {% if form.non_field_errors %}
                    <div class="bg-red-50 border-l-4 border-red-500 p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-500"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-red-700">
                                    {{ form.non_field_errors.0 }}
                                </p>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <!-- Submit Button -->
                <div>
                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
                        Create Account
                    </button>
                </div>
            </form>

            <!-- Login Link -->
            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600">
                    Already have an account?
                    <a href="{% url 'login' %}" class="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-300">
                        Log in
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
