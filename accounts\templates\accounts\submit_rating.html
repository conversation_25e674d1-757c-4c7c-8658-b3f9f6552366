{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Rate {{ rated_user.username }} - <PERSON><PERSON><PERSON>{% endblock %}

{% block body %}
<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Rating Form -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-8">
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Submit Your Rating</h2>
                <p class="text-gray-600">Your feedback helps build trust in our community.</p>

                {% if auction %}
                <div class="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <div class="flex items-start">
                        <!-- Auction Image -->
                        <div class="flex-shrink-0 w-16 h-16 overflow-hidden rounded-md">
                            {% if auction.image %}
                                <img src="{{ auction.image.url }}" alt="{{ auction.title }}" class="w-full h-full object-contain">
                            {% elif auction.image_url %}
                                <img src="{{ auction.image_url }}" alt="{{ auction.title }}" class="w-full h-full object-contain">
                            {% else %}
                                <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400"></i>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Auction Details -->
                        <div class="ml-4 flex-1">
                            <h3 class="text-sm font-medium text-gray-900">Rating for transaction:</h3>
                            <p class="text-sm text-gray-700 font-medium mt-1">{{ auction.title }}</p>
                            <div class="mt-1 flex items-center text-xs text-gray-500">
                                <span class="inline-flex items-center">
                                    <i class="fas fa-gavel mr-1"></i>
                                    Final Price: ${{ auction.price }}
                                </span>
                                <span class="mx-2">•</span>
                                <span class="inline-flex items-center">
                                    <i class="fas fa-calendar-alt mr-1"></i>
                                    Closed: {{ auction.updated_at|date:"M d, Y" }}
                                </span>
                            </div>

                            <!-- Transaction Role -->
                            <div class="mt-2">
                                {% if auction.user == rated_user %}
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-store mr-1"></i> You are rating as a Buyer
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        <i class="fas fa-shopping-cart mr-1"></i> You are rating as a Seller
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>

            <form action="{% url 'submit_rating' rated_user.username %}" method="post" class="space-y-6">
                {% csrf_token %}

                {% if auction_id %}
                <input type="hidden" name="auction_id" value="{{ auction_id }}">
                {% endif %}

                <!-- Rating Score -->
                <div>
                    <label for="{{ form.score.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ form.score.label }} <span class="text-red-500">*</span>
                    </label>

                    <!-- Interactive Star Rating -->
                    <div class="flex items-center mb-2">
                        <div class="star-rating flex space-x-1">
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-500 transition-colors duration-200" data-value="1">★</button>
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-500 transition-colors duration-200" data-value="2">★</button>
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-500 transition-colors duration-200" data-value="3">★</button>
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-500 transition-colors duration-200" data-value="4">★</button>
                            <button type="button" class="star-btn text-2xl text-gray-300 hover:text-yellow-500 transition-colors duration-200" data-value="5">★</button>
                        </div>
                        <span class="ml-3 text-sm text-gray-500 rating-text">Select a rating</span>
                    </div>

                    <!-- Hidden original select field -->
                    <div class="hidden">
                        {{ form.score }}
                    </div>

                    {% if form.score.errors %}
                        <p class="mt-1 text-xs text-red-600">{{ form.score.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Rating Type -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex items-center">
                        {{ form.as_seller }}
                        <label for="{{ form.as_seller.id_for_label }}" class="ml-2 block text-sm text-gray-700">
                            {{ form.as_seller.label }}
                        </label>
                    </div>
                    <div class="flex items-center">
                        {{ form.as_buyer }}
                        <label for="{{ form.as_buyer.id_for_label }}" class="ml-2 block text-sm text-gray-700">
                            {{ form.as_buyer.label }}
                        </label>
                    </div>
                </div>

                <!-- Comment -->
                <div>
                    <label for="{{ form.comment.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ form.comment.label }}
                    </label>
                    {{ form.comment }}
                    {% if form.comment.errors %}
                        <p class="mt-1 text-xs text-red-600">{{ form.comment.errors.0 }}</p>
                    {% endif %}
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <a href="{% url 'public_profile' rated_user.username %}" class="mr-4 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
                        Cancel
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300">
                        Submit Rating
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const starButtons = document.querySelectorAll('.star-btn');
        const ratingText = document.querySelector('.rating-text');
        const selectField = document.querySelector('select[name="score"]');

        // Rating descriptions
        const ratingDescriptions = {
            1: 'Poor - Very dissatisfied',
            2: 'Fair - Somewhat dissatisfied',
            3: 'Good - Neutral',
            4: 'Very Good - Satisfied',
            5: 'Excellent - Very satisfied'
        };

        // Set initial state if a value is already selected
        if (selectField.value) {
            updateStars(parseInt(selectField.value));
        }

        // Add click event to each star
        starButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const value = parseInt(this.getAttribute('data-value'));

                // Update the select field value
                selectField.value = value;

                // Update the stars
                updateStars(value);
            });
        });

        // Function to update stars based on selected value
        function updateStars(value) {
            starButtons.forEach(btn => {
                const btnValue = parseInt(btn.getAttribute('data-value'));

                if (btnValue <= value) {
                    btn.classList.add('text-yellow-500');
                    btn.classList.remove('text-gray-300');
                } else {
                    btn.classList.remove('text-yellow-500');
                    btn.classList.add('text-gray-300');
                }
            });

            // Update the rating text
            if (value > 0) {
                ratingText.textContent = ratingDescriptions[value];
            } else {
                ratingText.textContent = 'Select a rating';
            }
        }
    });
</script>
{% endblock %}