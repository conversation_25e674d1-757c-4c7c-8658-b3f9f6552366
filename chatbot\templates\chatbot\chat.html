{% load static %}
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> Chatbot - Ma<PERSON>i</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-glow': 'pulse-glow 2s ease-in-out infinite alternate',
                        'slide-up': 'slide-up 0.5s ease-out',
                        'fade-in': 'fade-in 0.3s ease-out',
                        'bounce-gentle': 'bounce-gentle 2s infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        },
                        'pulse-glow': {
                            '0%': { boxShadow: '0 0 20px rgba(59, 130, 246, 0.4)' },
                            '100%': { boxShadow: '0 0 30px rgba(59, 130, 246, 0.8)' },
                        },
                        'slide-up': {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        'fade-in': {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        'bounce-gentle': {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-5px)' },
                        },
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{% static 'chatbot/css/chat.css' %}" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 min-h-screen">
    <!-- Floating Background Elements -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-20 left-10 w-32 h-32 bg-blue-200 rounded-full opacity-20 animate-float"></div>
        <div class="absolute top-40 right-20 w-24 h-24 bg-purple-200 rounded-full opacity-30 animate-float" style="animation-delay: 2s;"></div>
        <div class="absolute bottom-40 left-1/4 w-20 h-20 bg-indigo-200 rounded-full opacity-25 animate-float" style="animation-delay: 4s;"></div>
    </div>

    <div class="container mx-auto px-4 py-8 relative z-10">
        <!-- Modern Header -->
        <div class="bg-white/80 backdrop-blur-lg rounded-3xl shadow-2xl mb-8 p-8 border border-white/20 animate-slide-up">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-6">
                    <div class="relative">
                        <div class="bg-gradient-to-br from-blue-500 to-purple-600 text-white p-4 rounded-2xl shadow-lg transform hover:scale-110 transition-all duration-300 animate-pulse-glow">
                            <i class="fas fa-robot text-2xl"></i>
                        </div>
                        <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full animate-bounce-gentle"></div>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Smart Chatbot
                        </h1>
                        <p class="text-gray-600 mt-1 text-lg">مساعدك الذكي في موقع المزادات - Your Smart Auction Assistant</p>
                        <div class="flex items-center mt-2 space-x-2">
                            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span class="text-sm text-green-600 font-medium">Online & Ready</span>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-3">
                    {% if user.is_authenticated %}
                        <a href="{% url 'chatbot:chat_history' %}" class="group bg-gradient-to-r from-gray-500 to-gray-600 text-white px-6 py-3 rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                            <i class="fas fa-history mr-2 group-hover:rotate-12 transition-transform duration-300"></i>History
                        </a>
                    {% endif %}
                    <a href="{% url 'index' %}" class="group bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                        <i class="fas fa-home mr-2 group-hover:rotate-12 transition-transform duration-300"></i>Home
                    </a>
                </div>
            </div>
        </div>

        <!-- Modern Chat Container -->
        <div class="bg-white/90 backdrop-blur-lg rounded-3xl shadow-2xl overflow-hidden border border-white/20 animate-fade-in" style="animation-delay: 0.2s;">
            <!-- Chat Header -->
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="relative">
                            <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-comments text-white text-xl"></i>
                            </div>
                            <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
                        </div>
                        <div>
                            <h3 class="text-white font-semibold text-lg">Live Chat</h3>
                            <p class="text-white/80 text-sm">AI Assistant is online</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-white/90 text-sm">Active</span>
                    </div>
                </div>
            </div>

            <!-- Chat Messages Area -->
            <div id="chat-messages" class="h-[500px] overflow-y-auto p-6 space-y-6 bg-gradient-to-b from-gray-50 to-white">
                <!-- Welcome Message -->
                <div class="flex items-start space-x-4 animate-slide-up">
                    <div class="relative">
                        <div class="bg-gradient-to-br from-blue-500 to-purple-600 text-white p-3 rounded-2xl shadow-lg">
                            <i class="fas fa-robot text-lg"></i>
                        </div>
                        <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"></div>
                    </div>
                    <div class="bg-white p-6 rounded-2xl shadow-lg max-w-md border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                        <div class="space-y-3">
                            <p class="text-gray-800 leading-relaxed">مرحباً بك! أنا مساعدك الذكي في موقع المزادات. كيف يمكنني مساعدتك اليوم؟</p>
                            <p class="text-gray-800 leading-relaxed">Hello! I'm your smart auction assistant. How can I help you today?</p>
                        </div>
                        <div class="flex items-center justify-between mt-4 pt-3 border-t border-gray-100">
                            <span class="text-xs text-gray-500">Just now</span>
                            <div class="flex items-center space-x-1">
                                <div class="w-1 h-1 bg-green-400 rounded-full"></div>
                                <span class="text-xs text-green-600">AI Assistant</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Chat Input Area -->
            <div class="bg-gradient-to-r from-gray-50 to-white p-6 border-t border-gray-100">
                <form id="chat-form" class="flex space-x-4">
                    <div class="flex-1 relative">
                        <div class="relative">
                            <input
                                type="text"
                                id="message-input"
                                placeholder="اكتب رسالتك هنا... Type your message here..."
                                class="w-full px-6 py-4 bg-white border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 text-gray-800 placeholder-gray-400 shadow-lg hover:shadow-xl"
                                autocomplete="off"
                            >
                            <div class="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span class="text-xs text-gray-400">Live</span>
                            </div>
                        </div>
                    </div>
                    <button
                        type="submit"
                        id="send-button"
                        class="group bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-2xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg"
                    >
                        <i class="fas fa-paper-plane group-hover:rotate-12 transition-transform duration-300"></i>
                    </button>
                </form>

                <!-- Enhanced Typing Indicator -->
                <div id="typing-indicator" class="hidden mt-4 flex items-center space-x-3 bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-white/40">
                    <div class="relative">
                        <div class="bg-gradient-to-br from-blue-500 to-purple-600 text-white p-2 rounded-xl">
                            <i class="fas fa-robot text-sm"></i>
                        </div>
                        <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"></div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                            <div class="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                            <div class="w-2 h-2 bg-indigo-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                        </div>
                        <span class="text-sm text-gray-600 font-medium">الذكي الاصطناعي يكتب... AI is typing...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Quick Actions -->
        <div class="mt-8 bg-white/80 backdrop-blur-lg rounded-3xl shadow-2xl p-8 border border-white/20 animate-fade-in" style="animation-delay: 0.4s;">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                    أسئلة شائعة - Quick Questions
                </h3>
                <p class="text-gray-600">اختر سؤالاً للحصول على إجابة فورية - Choose a question for instant answers</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Arabic Questions -->
                <button class="quick-question group bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-500 hover:to-blue-600 p-6 rounded-2xl text-right transition-all duration-300 transform hover:scale-105 hover:shadow-xl border border-blue-200 hover:border-blue-500" data-question="كيف أسجل في الموقع؟">
                    <div class="flex items-center justify-between">
                        <div class="bg-blue-500 group-hover:bg-white text-white group-hover:text-blue-500 p-3 rounded-xl transition-all duration-300 transform group-hover:rotate-12">
                            <i class="fas fa-user-plus text-lg"></i>
                        </div>
                        <div class="text-right">
                            <h4 class="font-semibold text-gray-800 group-hover:text-white transition-colors duration-300">كيف أسجل في الموقع؟</h4>
                            <p class="text-sm text-gray-600 group-hover:text-blue-100 transition-colors duration-300 mt-1">التسجيل والحساب</p>
                        </div>
                    </div>
                </button>

                <button class="quick-question group bg-gradient-to-br from-green-50 to-green-100 hover:from-green-500 hover:to-green-600 p-6 rounded-2xl text-right transition-all duration-300 transform hover:scale-105 hover:shadow-xl border border-green-200 hover:border-green-500" data-question="كيف أزايد على منتج؟">
                    <div class="flex items-center justify-between">
                        <div class="bg-green-500 group-hover:bg-white text-white group-hover:text-green-500 p-3 rounded-xl transition-all duration-300 transform group-hover:rotate-12">
                            <i class="fas fa-gavel text-lg"></i>
                        </div>
                        <div class="text-right">
                            <h4 class="font-semibold text-gray-800 group-hover:text-white transition-colors duration-300">كيف أزايد على منتج؟</h4>
                            <p class="text-sm text-gray-600 group-hover:text-green-100 transition-colors duration-300 mt-1">المزايدة والعروض</p>
                        </div>
                    </div>
                </button>

                <button class="quick-question group bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-500 hover:to-purple-600 p-6 rounded-2xl text-right transition-all duration-300 transform hover:scale-105 hover:shadow-xl border border-purple-200 hover:border-purple-500" data-question="ما هي طرق الدفع المتاحة؟">
                    <div class="flex items-center justify-between">
                        <div class="bg-purple-500 group-hover:bg-white text-white group-hover:text-purple-500 p-3 rounded-xl transition-all duration-300 transform group-hover:rotate-12">
                            <i class="fas fa-credit-card text-lg"></i>
                        </div>
                        <div class="text-right">
                            <h4 class="font-semibold text-gray-800 group-hover:text-white transition-colors duration-300">ما هي طرق الدفع؟</h4>
                            <p class="text-sm text-gray-600 group-hover:text-purple-100 transition-colors duration-300 mt-1">الدفع والتحويل</p>
                        </div>
                    </div>
                </button>

                <!-- English Questions -->
                <button class="quick-question group bg-gradient-to-br from-indigo-50 to-indigo-100 hover:from-indigo-500 hover:to-indigo-600 p-6 rounded-2xl text-left transition-all duration-300 transform hover:scale-105 hover:shadow-xl border border-indigo-200 hover:border-indigo-500" data-question="How to register?">
                    <div class="flex items-center justify-between">
                        <div class="bg-indigo-500 group-hover:bg-white text-white group-hover:text-indigo-500 p-3 rounded-xl transition-all duration-300 transform group-hover:rotate-12">
                            <i class="fas fa-user-plus text-lg"></i>
                        </div>
                        <div class="text-left">
                            <h4 class="font-semibold text-gray-800 group-hover:text-white transition-colors duration-300">How to register?</h4>
                            <p class="text-sm text-gray-600 group-hover:text-indigo-100 transition-colors duration-300 mt-1">Account & Registration</p>
                        </div>
                    </div>
                </button>

                <button class="quick-question group bg-gradient-to-br from-teal-50 to-teal-100 hover:from-teal-500 hover:to-teal-600 p-6 rounded-2xl text-left transition-all duration-300 transform hover:scale-105 hover:shadow-xl border border-teal-200 hover:border-teal-500" data-question="How to bid on items?">
                    <div class="flex items-center justify-between">
                        <div class="bg-teal-500 group-hover:bg-white text-white group-hover:text-teal-500 p-3 rounded-xl transition-all duration-300 transform group-hover:rotate-12">
                            <i class="fas fa-gavel text-lg"></i>
                        </div>
                        <div class="text-left">
                            <h4 class="font-semibold text-gray-800 group-hover:text-white transition-colors duration-300">How to bid on items?</h4>
                            <p class="text-sm text-gray-600 group-hover:text-teal-100 transition-colors duration-300 mt-1">Bidding & Offers</p>
                        </div>
                    </div>
                </button>

                <button class="quick-question group bg-gradient-to-br from-rose-50 to-rose-100 hover:from-rose-500 hover:to-rose-600 p-6 rounded-2xl text-left transition-all duration-300 transform hover:scale-105 hover:shadow-xl border border-rose-200 hover:border-rose-500" data-question="Payment methods?">
                    <div class="flex items-center justify-between">
                        <div class="bg-rose-500 group-hover:bg-white text-white group-hover:text-rose-500 p-3 rounded-xl transition-all duration-300 transform group-hover:rotate-12">
                            <i class="fas fa-credit-card text-lg"></i>
                        </div>
                        <div class="text-left">
                            <h4 class="font-semibold text-gray-800 group-hover:text-white transition-colors duration-300">Payment methods?</h4>
                            <p class="text-sm text-gray-600 group-hover:text-rose-100 transition-colors duration-300 mt-1">Payment & Transfer</p>
                        </div>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{% static 'chatbot/js/chat.js' %}"></script>
</body>
</html>
