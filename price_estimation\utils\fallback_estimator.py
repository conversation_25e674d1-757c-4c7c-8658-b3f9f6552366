"""
Fallback estimation utilities.

Note: This module has been disabled to ensure only real scraped prices are used.
No hardcoded or estimated fallback prices are provided.
"""

import logging

logger = logging.getLogger('price_estimation')


class FallbackEstimator:
    """
    Disabled fallback estimator - no longer provides hardcoded price estimates.
    
    This class has been modified to only return clear error messages when
    scraping fails, ensuring users only see real market prices.
    """

    def __init__(self):
        pass

    def estimate_dubizzle_fallback(self, name: str, description: str) -> str:
        """
        Returns a clear message that no price was found instead of providing
        hardcoded fallback estimates.
        
        Args:
            name: Product name (unused - kept for compatibility)
            description: Product description (unused - kept for compatibility)
        """
        # Parameters are intentionally unused - kept for API compatibility
        _ = name, description
        logger.info("Dubizzle scraping failed - no fallback pricing provided")
        return "No price found on Dubizzle"
