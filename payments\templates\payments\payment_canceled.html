{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Payment Canceled - Mazadi{% endblock %}

{% block body %}
<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <div class="p-8 text-center">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
                <i class="fas fa-times-circle text-3xl text-red-600"></i>
            </div>
            
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Payment Not Completed</h2>
            <p class="text-lg text-gray-600 mb-8">
                Your payment for <span class="font-semibold">{{ auction.title }}</span> was canceled or not completed.
            </p>
            
            <div class="bg-gray-50 p-6 rounded-lg mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-3">Payment Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                    <div>
                        <p class="text-sm text-gray-500">Payment ID</p>
                        <p class="font-medium text-gray-900">{{ payment.id }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Date</p>
                        <p class="font-medium text-gray-900">{{ payment.created_at|date:"F j, Y" }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Status</p>
                        <p class="font-medium text-red-600">Canceled</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Amount</p>
                        <p class="font-medium text-gray-900">${{ payment.amount }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8 text-left">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            Don't worry! No money has been charged. You can try again or contact support if you need assistance.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <a href="{% url 'payment_process' auction.id %}" class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                    <i class="fas fa-redo mr-2"></i> Try Again
                </a>
                <a href="{% url 'auction' auction.id %}" class="inline-flex items-center justify-center px-5 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Auction
                </a>
            </div>
        </div>
    </div>
    
    <!-- Need Help Section -->
    <div class="mt-8 bg-white rounded-lg shadow-lg overflow-hidden">
        <div class="p-8">
            <h3 class="text-xl font-bold text-gray-900 mb-4">Need Help?</h3>
            
            <div class="space-y-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <div class="flex items-center justify-center h-8 w-8 rounded-full bg-primary-100 text-primary-600">
                            <i class="fas fa-question-circle"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-lg font-medium text-gray-900">Common Issues</h4>
                        <p class="mt-1 text-gray-600">
                            Payment might be canceled due to incorrect card information, insufficient funds, or technical issues.
                        </p>
                    </div>
                </div>
                
                <div class="flex">
                    <div class="flex-shrink-0">
                        <div class="flex items-center justify-center h-8 w-8 rounded-full bg-primary-100 text-primary-600">
                            <i class="fas fa-credit-card"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-lg font-medium text-gray-900">Test Card Information</h4>
                        <p class="mt-1 text-gray-600">
                            Remember, this is a test environment. Use card number 4242 4242 4242 4242 with any future expiration date and any 3-digit CVC.
                        </p>
                    </div>
                </div>
                
                <div class="flex">
                    <div class="flex-shrink-0">
                        <div class="flex items-center justify-center h-8 w-8 rounded-full bg-primary-100 text-primary-600">
                            <i class="fas fa-envelope"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h4 class="text-lg font-medium text-gray-900">Contact Support</h4>
                        <p class="mt-1 text-gray-600">
                            If you continue to experience issues, please contact our support team for assistance.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
