{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Message Inbox - Mazadi{% endblock %}

{% block body %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Message Threads -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Your Conversations</h2>
        </div>
        
        {% if threads %}
            <ul class="divide-y divide-gray-200">
                {% for thread in threads %}
                    <li class="hover:bg-gray-50 transition-colors duration-200 {% if thread.unread_count > 0 %}bg-blue-50{% endif %}">
                        <a href="{% url 'messages_thread' thread.payment.id %}" class="block">
                            <div class="px-6 py-5 flex items-center">
                                <!-- User Avatar -->
                                <div class="flex-shrink-0">
                                    {% if thread.other_user.profile.profile_picture %}
                                        <img src="{{ thread.other_user.profile.profile_picture.url }}" alt="{{ thread.other_user.username }}" class="h-12 w-12 rounded-full object-cover border border-primary-300">
                                    {% else %}
                                        <div class="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center">
                                            <i class="fas fa-user text-primary-500 text-lg"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Thread Info -->
                                <div class="ml-4 flex-1">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-base font-medium text-gray-900">
                                            {{ thread.other_user.username }}
                                            {% if thread.unread_count > 0 %}
                                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                                    {{ thread.unread_count }} new
                                                </span>
                                            {% endif %}
                                        </h3>
                                        <p class="text-sm text-gray-500">
                                            {% if thread.latest_message %}
                                                {{ thread.latest_message.created_at|date:"M d, Y" }}
                                            {% else %}
                                                {{ thread.payment.created_at|date:"M d, Y" }}
                                            {% endif %}
                                        </p>
                                    </div>
                                    
                                    <div class="mt-1">
                                        <p class="text-sm text-gray-600 truncate">
                                            {% if thread.latest_message %}
                                                <span class="font-medium">{{ thread.latest_message.sender.username }}:</span> {{ thread.latest_message.content|truncatechars:60 }}
                                            {% else %}
                                                <span class="italic text-gray-500">No messages yet. Start a conversation!</span>
                                            {% endif %}
                                        </p>
                                    </div>
                                    
                                    <div class="mt-2 flex items-center text-xs text-gray-500">
                                        <span class="flex items-center">
                                            <i class="fas fa-tag mr-1"></i>
                                            {{ thread.payment.auction.title|truncatechars:40 }}
                                        </span>
                                        <span class="mx-2">•</span>
                                        <span class="flex items-center">
                                            <i class="fas fa-dollar-sign mr-1"></i>
                                            ${{ thread.payment.amount }}
                                        </span>
                                        <span class="mx-2">•</span>
                                        <span class="flex items-center">
                                            {% if thread.payment.user == request.user %}
                                                <i class="fas fa-shopping-cart mr-1"></i> You purchased
                                            {% else %}
                                                <i class="fas fa-store mr-1"></i> You sold
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>
                                
                                <!-- Arrow Icon -->
                                <div class="ml-4">
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                        </a>
                    </li>
                {% endfor %}
            </ul>
        {% else %}
            <div class="p-12 text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
                    <i class="far fa-comment-dots text-gray-400 text-2xl"></i>
                </div>
                <p class="text-gray-500 mb-4">You don't have any message threads yet.</p>
                <p class="text-gray-500">Message threads are created automatically when you complete a payment.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
