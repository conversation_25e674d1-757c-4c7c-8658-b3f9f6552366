{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}{{ auction.title }} - <PERSON><PERSON><PERSON>{% endblock %}

{% block body %}
<!-- Breadcrumb -->
<div class="bg-gradient-to-r from-gray-50 to-gray-100 py-4 border-b border-gray-200 shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <div>
                        <a href="{% url 'home' %}" class="text-gray-500 hover:text-primary-600 transition-all duration-300 transform hover:scale-110 inline-block">
                            <i class="fas fa-home"></i>
                            <span class="sr-only">Home</span>
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs animate-pulse-slow"></i>
                        <a href="{% url 'index' %}" class="ml-4 text-gray-500 hover:text-primary-600 transition-all duration-300 hover:underline">Auctions</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs animate-pulse-slow"></i>
                        <a href="{% url 'page' auction.category %}" class="ml-4 text-gray-500 hover:text-primary-600 transition-all duration-300 hover:underline">
                            <span class="inline-flex items-center">
                                <i class="fas fa-tag text-xs mr-1"></i>
                                {{ auction.category }}
                            </span>
                        </a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 text-xs animate-pulse-slow"></i>
                        <span class="ml-4 text-primary-700 font-medium truncate max-w-xs bg-primary-50 px-2 py-1 rounded-md">
                            {{ auction.title }}
                        </span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>
</div>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fade-in">
    <!-- Auction Status Banner -->
    {% if auction.is_close %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-gavel text-red-500 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">This auction has ended</h3>
                    <div class="mt-1 text-sm text-red-700">
                        {% if user.id == bid.user.id %}
                            Congratulations! You won this auction with a bid of EGP{{ bid.amount }}.
                        {% else %}
                            This auction was won by {{ bid.user.username }} with a bid of EGP{{ bid.amount }}.
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Post-Auction Rating Buttons -->
            {% if bid and user.is_authenticated %}
                <div class="mt-4 flex flex-wrap gap-2 justify-end">
                    {% if user.id == bid.user.id %}
                        <!-- Winner can rate the seller -->
                        <a href="{% url 'submit_rating' auction.user.username %}?auction_id={{ auction.id }}"
                           class="inline-flex items-center px-3 py-2 border border-primary-600 text-primary-600 rounded-md hover:bg-primary-600 hover:text-white transition-all duration-300">
                            <i class="fas fa-star-half-alt mr-2"></i> Rate Seller
                        </a>
                    {% elif user.id == auction.user.id %}
                        <!-- Seller can rate the winner -->
                        <a href="{% url 'submit_rating' bid.user.username %}?auction_id={{ auction.id }}"
                           class="inline-flex items-center px-3 py-2 border border-primary-600 text-primary-600 rounded-md hover:bg-primary-600 hover:text-white transition-all duration-300">
                            <i class="fas fa-star-half-alt mr-2"></i> Rate Buyer
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    {% elif auction.is_close == False %}
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-8 flex items-center">
            <div class="flex-shrink-0">
                <i class="fas fa-clock text-green-500 text-xl"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">Active Auction</h3>
                <div class="mt-1 text-sm text-green-700">
                    This auction is currently active. Current bid: EGP{{ bid.amount }}
                </div>
            </div>
        </div>
    {% endif %}

    <div class="grid grid-cols-1 lg:grid-cols-5 gap-8 mb-12">
        <!-- Left Column - Image Gallery -->
        <div class="lg:col-span-3">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg">
                <!-- Main Image with Zoom Feature -->
                <div class="relative h-96 bg-gray-100 overflow-hidden" id="image-container">
                    <div class="image-zoom-container w-full h-full relative cursor-zoom-in">
                        {% if auction.image %}
                            <img src="{{ auction.image.url }}"
                                 alt="{{ auction.title }}"
                                 id="zoomable-image"
                                 class="w-full h-full object-contain object-center transition-all duration-300 ease-in-out"
                                 loading="lazy">
                        {% elif auction.image_url %}
                            <img src="{{ auction.image_url }}"
                                 alt="{{ auction.title }}"
                                 id="zoomable-image"
                                 class="w-full h-full object-contain object-center transition-all duration-300 ease-in-out"
                                 loading="lazy">
                        {% else %}
                            <img src="/static/auctions/placeholder.png"
                                 alt="{{ auction.title }}"
                                 id="zoomable-image"
                                 class="w-full h-full object-contain object-center transition-all duration-300 ease-in-out"
                                 loading="lazy">
                        {% endif %}

                        <!-- Zoom Lens -->
                        <div id="zoom-lens" class="hidden absolute border-2 border-primary-500 bg-primary-100 bg-opacity-30 pointer-events-none rounded-lg shadow-lg backdrop-blur-sm"></div>

                        <!-- Zoom Result -->
                        <div id="zoom-result" class="hidden absolute top-0 left-0 right-0 bottom-0 bg-contain bg-no-repeat bg-center z-10 rounded-lg shadow-2xl border border-gray-300"></div>
                    </div>

                    <!-- Status Badge -->
                    <div class="absolute top-4 right-4 z-20">
                        {% if auction.is_close %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Closed</span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                        {% endif %}
                    </div>

                    <!-- Zoom Instructions -->
                    <div class="absolute bottom-4 left-4 z-20 bg-black bg-opacity-70 text-white text-xs px-3 py-2 rounded-lg flex items-center backdrop-blur-sm transition-all duration-300 hover:bg-opacity-80">
                        <i class="fas fa-search-plus mr-2 text-primary-300"></i>
                        <span>Click to zoom • Move to explore</span>
                    </div>
                </div>

                <!-- Thumbnail Gallery (Placeholder for future enhancement) -->
                <div class="flex p-2 bg-gray-50 border-t border-gray-200">
                    <div class="w-20 h-20 border border-primary-300 rounded overflow-hidden">
                        {% if auction.image %}
                            <img src="{{ auction.image.url }}" alt="{{ auction.title }}" class="w-full h-full object-contain" loading="lazy">
                        {% elif auction.image_url %}
                            <img src="{{ auction.image_url }}" alt="{{ auction.title }}" class="w-full h-full object-contain" loading="lazy">
                        {% else %}
                            <img src="/static/auctions/placeholder.png" alt="{{ auction.title }}" class="w-full h-full object-contain" loading="lazy">
                        {% endif %}
                    </div>
                    <div class="w-20 h-20 mx-2 border border-gray-200 rounded overflow-hidden opacity-50 cursor-pointer hover:opacity-100 transition duration-300">
                        <div class="w-full h-full flex items-center justify-center bg-gray-100">
                            <i class="fas fa-plus text-gray-400"></i>
                        </div>
                    </div>
                    <div class="w-20 h-20 border border-gray-200 rounded overflow-hidden opacity-50 cursor-pointer hover:opacity-100 transition duration-300">
                        <div class="w-full h-full flex items-center justify-center bg-gray-100">
                            <i class="fas fa-plus text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Seller Information -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-300 mt-6 transform hover:shadow-md hover:-translate-y-1">
                <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200 flex items-center">
                    <i class="fas fa-user-circle text-primary-500 mr-2"></i>
                    <h3 class="text-lg font-medium">Seller Information</h3>
                </div>
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            {% if auction.user.profile.profile_picture %}
                                <img src="{{ auction.user.profile.profile_picture.url }}" alt="{{ auction.user.username }}" class="w-16 h-16 rounded-full object-cover border-2 border-primary-300 transform transition-all duration-500 hover:scale-110 hover:rotate-6">
                            {% else %}
                                <div class="w-16 h-16 rounded-full bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center transform transition-all duration-500 hover:scale-110 hover:rotate-6 group">
                                    <i class="fas fa-user text-primary-500 transition-all duration-500 group-hover:text-primary-600"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="ml-6">
                            <h4 class="text-lg font-medium text-gray-900 flex items-center">
                                <a href="{% url 'public_profile' auction.user.username %}" class="hover:text-primary-600 transition-colors duration-300">
                                    {{ auction.user.first_name }} {{ auction.user.last_name }}
                                </a>
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                    <i class="fas fa-check-circle mr-1"></i> Verified Seller
                                </span>
                            </h4>
                            <!-- Seller Rating -->
                            <div class="flex items-center mt-1">
                                <div class="flex items-center">
                                    {% if auction.user.profile.seller_rating_avg > 0 %}
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= auction.user.profile.seller_rating_avg %}
                                                <i class="fas fa-star text-yellow-500 text-xs"></i>
                                            {% else %}
                                                <i class="far fa-star text-yellow-500 text-xs"></i>
                                            {% endif %}
                                        {% endfor %}
                                        <span class="ml-1 text-xs text-gray-500">({{ auction.user.profile.total_ratings_count }})</span>
                                    {% else %}
                                        <span class="text-xs text-gray-500">No ratings yet</span>
                                    {% endif %}
                                </div>
                            </div>
                            <p class="text-gray-500 flex items-center mt-1">
                                <i class="far fa-calendar-alt text-gray-400 mr-2"></i>
                                Member since {{ auction.user.date_joined|date:"F Y" }}
                            </p>
                            <div class="mt-3 flex space-x-3">
                                <a href="{% url 'public_profile' auction.user.username %}" class="text-sm text-primary-600 hover:text-primary-800 transition-colors duration-300 flex items-center">
                                    <i class="fas fa-user mr-1"></i> View Profile
                                </a>
                                <a href="{% url 'user_ratings' auction.user.username %}" class="text-sm text-primary-600 hover:text-primary-800 transition-colors duration-300 flex items-center">
                                    <i class="far fa-star mr-1"></i> View Ratings
                                </a>
                                {% if user.is_authenticated and user.id != auction.user.id %}
                                <a href="{% url 'submit_rating' auction.user.username %}?auction_id={{ auction.id }}" class="text-sm text-primary-600 hover:text-primary-800 transition-colors duration-300 flex items-center">
                                    <i class="fas fa-star-half-alt mr-1"></i> Rate Seller
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Auction Details -->
        <div class="lg:col-span-2">
            <!-- Auction Details Card -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-300 sticky top-24 transform hover:shadow-md">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <h1 class="text-2xl font-bold text-gray-900 group">
                            <span class="bg-clip-text text-transparent bg-gradient-to-r from-primary-600 to-primary-800">{{ auction.title }}</span>
                        </h1>
                        {% if user.id == auction.user.id %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 animate-pulse-slow">
                                <i class="fas fa-user-check mr-1"></i> Your Listing
                            </span>
                        {% endif %}
                    </div>

                    <!-- Price -->
                    <div class="flex items-baseline mb-6 group">
                        <span class="text-3xl font-bold text-primary-600 transition-all duration-300 group-hover:scale-105">EGP{{ auction.price }}</span>
                        <span class="ml-2 text-sm text-gray-500">Current Price</span>
                    </div>

                    <!-- Bid Information -->
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-5 mb-6 shadow-inner transform transition-all duration-300 hover:shadow">
                        <div class="flex justify-between items-center mb-3">
                            <span class="text-gray-700 font-medium flex items-center">
                                <i class="fas fa-gavel text-primary-500 mr-2"></i>Current Bid:
                            </span>
                            <span class="text-lg font-bold text-primary-600 bg-primary-50 px-3 py-1 rounded-full">EGP{{ bid.amount }}</span>
                        </div>
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-gray-700 font-medium flex items-center">
                                <i class="fas fa-user text-primary-500 mr-2"></i>Bid Placed By:
                            </span>
                            <span class="text-gray-900 font-medium flex items-center">
                                {{ bid.user.first_name }} {{ bid.user.last_name }}
                                {% if bid.user.profile.profile_picture %}
                                    <img src="{{ bid.user.profile.profile_picture.url }}" alt="{{ bid.user.username }}" class="h-6 w-6 rounded-full object-cover border border-primary-200 ml-2">
                                {% endif %}
                            </span>
                        </div>

                        {% if auction.is_close == False and user.is_authenticated %}
                            <form action="{% url 'bid' %}" method="post" class="space-y-3">
                                {% csrf_token %}
                                <div class="relative group">
                                    {{ BidForm }}
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-500 sm:text-sm">EGP</span>
                                    </div>
                                    <div class="absolute inset-0 border border-gray-300 border-opacity-0 rounded-md group-focus-within:border-primary-500 group-focus-within:border-opacity-100 transition-all duration-300 pointer-events-none"></div>
                                </div>
                                <input type="hidden" name="auction_id" value="{{ auction.id }}">
                                <button type="submit" class="w-full flex items-center justify-center bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-md shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 group">
                                    <i class="fas fa-gavel mr-2 transition-transform duration-300 group-hover:rotate-12"></i> Place Bid
                                </button>
                            </form>
                        {% elif auction.is_close == False and not user.is_authenticated %}
                            <div class="text-center py-3 bg-primary-50 rounded-md">
                                <a href="{% url 'login' %}" class="text-primary-600 hover:text-primary-800 font-medium inline-flex items-center group">
                                    <i class="fas fa-sign-in-alt mr-2 transition-transform duration-300 group-hover:translate-x-[-2px]"></i>
                                    Log in to place a bid
                                </a>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Product Details -->
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-info-circle text-primary-500 mr-2"></i>Product Details
                        </h3>
                        <div class="space-y-3 text-gray-700 bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center border-b border-gray-200 pb-2 group">
                                <span class="text-gray-500 flex items-center">
                                    <i class="fas fa-tag text-gray-400 mr-2 transition-transform duration-300 group-hover:rotate-12"></i>
                                    Category:
                                </span>
                                <span class="font-medium text-primary-700 transition-colors duration-300 group-hover:text-primary-600">{{ auction.category }}</span>
                            </div>
                            <div class="flex justify-between items-center border-b border-gray-200 pb-2 group">
                                <span class="text-gray-500 flex items-center">
                                    <i class="far fa-calendar-alt text-gray-400 mr-2 transition-transform duration-300 group-hover:scale-110"></i>
                                    Listed On:
                                </span>
                                <span class="font-medium">{{ auction.created_at|date:"F d, Y" }}</span>
                            </div>
                            <div class="flex justify-between items-center group">
                                <span class="text-gray-500 flex items-center">
                                    <i class="fas fa-circle text-gray-400 mr-2 transition-all duration-300 group-hover:text-xs"></i>
                                    Status:
                                </span>
                                <span class="font-medium {% if auction.is_close %}text-red-600{% else %}text-green-600{% endif %}">
                                    {% if auction.is_close %}
                                        <i class="fas fa-times-circle mr-1"></i> Closed
                                    {% else %}
                                        <i class="fas fa-check-circle mr-1"></i> Active
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-align-left text-primary-500 mr-2"></i>Description
                        </h3>
                        <div class="text-gray-700 bg-gray-50 p-4 rounded-lg border-l-2 border-primary-300">
                            {{ auction.description }}
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        {% if not watchlisted and user.is_authenticated %}
                            <form action="{% url 'watchlist' %}" method="post">
                                {% csrf_token %}
                                <input type="hidden" name="auction_id" value="{{ auction.id }}">
                                <button type="submit" class="w-full flex items-center justify-center px-4 py-3 border-2 border-primary-600 text-primary-600 font-medium rounded-lg hover:bg-primary-600 hover:text-white transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 group">
                                    <i class="far fa-heart mr-2 transition-all duration-300 group-hover:scale-125 group-hover:animate-pulse"></i> Add to Watchlist
                                </button>
                            </form>
                        {% elif watchlisted %}
                            <form action="{% url 'remove' %}" method="post">
                                {% csrf_token %}
                                <input type="hidden" name="auction_id" value="{{ auction.id }}">
                                <button type="submit" class="w-full flex items-center justify-center px-4 py-3 border-2 border-red-600 text-red-600 font-medium rounded-lg hover:bg-red-600 hover:text-white transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 group">
                                    <i class="fas fa-heart mr-2 animate-pulse"></i> Remove from Watchlist
                                </button>
                            </form>
                        {% endif %}

                        <!-- Owner Action Buttons -->
                        {% if user.id == auction.user.id and auction.is_close == False %}
                            <!-- Close Auction Button -->
                            <form action="{% url 'close' auction.id %}" method="post" class="mb-3">
                                {% csrf_token %}
                                <button type="submit" class="w-full flex items-center justify-center px-4 py-3 bg-red-600 text-white font-medium rounded-lg hover:bg-orange-700 transition-all duration-300 transform hover:scale-[1.02] shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 group">
                                    <i class="fas fa-times-circle mr-2 transition-transform duration-300 group-hover:rotate-12"></i> Close Auction
                                </button>
                            </form>

                            <div class="bg-red-50 border-l-4 border-red-500 p-3 rounded mt-3 animate-pulse-slow">
                                <p class="text-sm text-red-600 flex items-center">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    Warning: Close and delete actions cannot be undone.
                                </p>
                            </div>
                        {% endif %}

                        <!-- Payment Button (For Auction Winner) -->
                        {% if auction.is_close and user.is_authenticated %}
                            {% with highest_bid=auction.bids.all|dictsort:"amount"|last %}
                                {% if highest_bid.user.id == user.id %}
                                    <div class="mt-4">
                                        <a href="{% url 'payment_process' auction.id %}" class="w-full flex items-center justify-center px-4 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-all duration-300 transform hover:scale-[1.02] shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 group">
                                            <i class="fas fa-credit-card mr-2 transition-transform duration-300 group-hover:rotate-12"></i> Pay Now
                                        </a>

                                        <div class="bg-green-50 border-l-4 border-green-500 p-3 rounded mt-3">
                                            <p class="text-sm text-green-600 flex items-center">
                                                <i class="fas fa-info-circle mr-2"></i>
                                                Congratulations! You won this auction. Complete your purchase now.
                                            </p>
                                        </div>
                                    </div>
                                {% endif %}
                            {% endwith %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Comments Section -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-300 mb-8 transform hover:shadow-md">
        <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200 flex items-center">
            <i class="far fa-comments text-primary-500 mr-2 transform transition-all duration-300 group-hover:scale-110"></i>
            <h3 class="text-lg font-medium">Comments ({{ comments.count }})</h3>
        </div>

        <!-- Comments List -->
        <div class="divide-y divide-gray-200">
            {% for comment in comments %}
                <div class="p-6 hover:bg-gray-50 transition-colors duration-300 animate-fade-in" style="animation-delay: {{ forloop.counter0 }}00ms">
                    <div class="flex space-x-3">
                        <div class="flex-shrink-0">
                            {% if comment.user.profile.profile_picture %}
                                <img src="{{ comment.user.profile.profile_picture.url }}" alt="{{ comment.user.username }}" class="h-10 w-10 rounded-full object-cover border border-primary-200 transform transition-transform duration-300 hover:scale-110 hover:rotate-3">
                            {% else %}
                                <div class="h-10 w-10 rounded-full bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center transform transition-transform duration-300 hover:scale-110 hover:rotate-3">
                                    <i class="fas fa-user text-primary-500"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 flex items-center">
                                <a href="{% url 'public_profile' comment.user.username %}" class="hover:text-primary-600 transition-colors duration-300">
                                    {{ comment.user.first_name }} {{ comment.user.last_name }}
                                </a>
                                {% if comment.user.id == auction.user.id %}
                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-100 text-primary-800">
                                        Seller
                                    </span>
                                {% endif %}
                            </p>
                            <p class="text-sm text-gray-500 flex items-center">
                                <i class="far fa-clock text-gray-400 mr-1 text-xs"></i>
                                {{ comment.created_at|date:"F d, Y" }} at {{ comment.created_at|time:"g:i A" }}
                            </p>
                            <div class="mt-2 text-gray-700 p-3 bg-gray-50 rounded-lg border-l-2 border-primary-200">
                                {{ comment.message }}
                            </div>
                        </div>
                    </div>
                </div>
            {% empty %}
                <div class="p-12 text-center animate-fade-in">
                    <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
                        <i class="far fa-comment-dots text-gray-400 text-2xl"></i>
                    </div>
                    <p class="text-gray-500 italic">No comments yet. Be the first to comment!</p>
                </div>
            {% endfor %}
        </div>

        <!-- Comment Form -->
        {% if auction.is_close == False and user.is_authenticated %}
            <div class="px-6 py-6 bg-gradient-to-r from-gray-50 to-gray-100 border-t border-gray-200">
                <form action="{% url 'comment' %}" method="post" class="space-y-4">
                    {% csrf_token %}
                    <div class="relative group">
                        {{ CommentForm }}
                        <input type="hidden" name="auction_id" value="{{ auction.id }}">
                        <div class="absolute inset-0 border border-gray-300 border-opacity-0 rounded-md group-focus-within:border-primary-500 group-focus-within:border-opacity-100 transition-all duration-300 pointer-events-none"></div>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="flex items-center bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md shadow-sm transition-all duration-300 transform hover:translate-y-[-2px] hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="far fa-paper-plane mr-2 transition-transform duration-300 group-hover:translate-x-1"></i>
                            Post Comment
                        </button>
                    </div>
                </form>
            </div>
        {% elif not user.is_authenticated %}
            <div class="px-6 py-6 bg-gradient-to-r from-gray-50 to-gray-100 border-t border-gray-200 text-center">
                <a href="{% url 'login' %}" class="inline-flex items-center text-primary-600 hover:text-primary-800 font-medium transition-colors duration-300 group">
                    <i class="fas fa-sign-in-alt mr-2 transition-transform duration-300 group-hover:translate-x-[-2px]"></i>
                    Log in to post a comment
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Related Auctions -->
    <div class="mb-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Auctions</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            {% for related in related_auctions %}
            <div class="animate-slide-in" style="animation-delay: {{ forloop.counter0 }}00ms">
                <div class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl h-full flex flex-col group">
                    <!-- Image container with enhanced hover effects -->
                    <div class="h-48 overflow-hidden relative">
                        <!-- Status badge -->
                        <div class="absolute top-3 right-3 z-10">
                            {% if related.is_close %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Closed</span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                            {% endif %}
                        </div>

                        <!-- Image with hover zoom effect -->
                        <div class="w-full h-full overflow-hidden group">
                            {% if related.image %}
                                <img src="{{ related.image.url }}" alt="{{ related.title }}" class="w-full h-full object-contain transform group-hover:scale-110 transition-transform duration-500" loading="lazy">
                            {% elif related.image_url %}
                                <img src="{{ related.image_url }}" alt="{{ related.title }}" class="w-full h-full object-contain transform group-hover:scale-110 transition-transform duration-500" loading="lazy">
                            {% else %}
                                <img src="/static/auctions/placeholder.png"
                                     alt="{{ related.title }}"
                                     class="w-full h-full object-contain transform group-hover:scale-110 transition-transform duration-500"
                                     loading="lazy"
                                >
                            {% endif %}
                        </div>
                    </div>

                    <!-- Content with enhanced styling -->
                    <div class="p-6 flex flex-col flex-grow">
                        <div class="mb-3">
                            <h3 class="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{ related.title }}</h3>
                            <p class="text-gray-600 mt-2 line-clamp-2">{{ related.description }}</p>
                        </div>

                        <div class="mt-auto">
                            <!-- Price with animation -->
                            <div class="flex justify-end mb-4">
                                <div class="bg-primary-50 px-3 py-1 rounded-full">
                                    <span class="text-lg font-bold text-primary-700 group-hover:text-primary-800 transition-colors duration-300 inline-block transform group-hover:scale-110 transition-transform">
                                        EGP{{ related.price }}
                                    </span>
                                </div>
                            </div>

                            <!-- CTA Button with enhanced hover effect -->
                            <a href="{% url 'auction' related.id %}" class="relative overflow-hidden block w-full text-center px-4 py-3 border-2 border-primary-600 text-primary-600 rounded-md bg-transparent group-hover:bg-primary-600 group-hover:text-white transition-all duration-300 group-hover:shadow-lg group-hover:border-primary-700 transform group-hover:scale-105">
                                <span class="relative z-10 flex items-center justify-center font-medium">
                                    <span>View Details</span>
                                    <i class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-1"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
                <div class="col-span-full">
                    <div class="bg-gray-50 rounded-lg p-8 text-center">
                        <i class="fas fa-search text-gray-400 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-700 mb-2">No Related Auctions Found</h3>
                        <p class="text-gray-500">We couldn't find any other active auctions in this category.</p>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get elements
        const container = document.getElementById('image-container');
        const img = document.getElementById('zoomable-image');
        const lens = document.getElementById('zoom-lens');
        const result = document.getElementById('zoom-result');

        // Preload main image
        if (img) {
            // Show loading indicator
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'absolute inset-0 bg-gray-100 flex items-center justify-center z-10 transition-opacity duration-300';
            loadingOverlay.innerHTML = '<div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>';
            loadingOverlay.id = 'loading-overlay';
            container.appendChild(loadingOverlay);

            // When image is loaded, remove the loading indicator
            img.onload = function() {
                const overlay = document.getElementById('loading-overlay');
                if (overlay) {
                    overlay.classList.add('opacity-0');
                    setTimeout(() => {
                        overlay.remove();
                    }, 300);
                }
            };

            // If image is already cached, trigger onload manually
            if (img.complete) {
                img.onload();
            }
        }

        // Variables
        let isZoomed = false;
        const zoomLevel = 2.5; // Zoom magnification level

        // Set lens size (adjust as needed)
        const lensWidth = 100;
        const lensHeight = 100;

        // Initialize zoom functionality
        function initZoom() {
            if (!img) return;

            // Toggle zoom on click
            container.addEventListener('click', function(e) {
                if (!isZoomed) {
                    // Enable zoom mode
                    isZoomed = true;
                    lens.classList.remove('hidden');
                    result.classList.remove('hidden');
                    result.style.backgroundImage = `url('${img.src}')`;
                    container.classList.add('cursor-crosshair');
                    container.classList.remove('cursor-zoom-in');

                    // Set initial lens position
                    moveLens(e);
                } else {
                    // Disable zoom mode
                    isZoomed = false;
                    lens.classList.add('hidden');
                    result.classList.add('hidden');
                    container.classList.remove('cursor-crosshair');
                    container.classList.add('cursor-zoom-in');
                }
            });

            // Move lens on mousemove when zoomed
            container.addEventListener('mousemove', function(e) {
                if (isZoomed) {
                    moveLens(e);
                }
            });

            // Disable zoom when mouse leaves container
            container.addEventListener('mouseleave', function() {
                isZoomed = false;
                lens.classList.add('hidden');
                result.classList.add('hidden');
                container.classList.remove('cursor-crosshair');
                container.classList.add('cursor-zoom-in');
            });
        }

        // Move lens and update zoom result
        function moveLens(e) {
            // Prevent default behavior
            e.preventDefault();

            // Get cursor position
            const pos = getCursorPos(e);

            // Get container dimensions
            const containerRect = container.getBoundingClientRect();
            const imgRect = img.getBoundingClientRect();

            // Calculate lens position relative to the image
            let x = pos.x - (lensWidth / 2);
            let y = pos.y - (lensHeight / 2);

            // Prevent lens from going outside the image bounds
            const maxX = imgRect.width - lensWidth;
            const maxY = imgRect.height - lensHeight;

            if (x > maxX) x = maxX;
            if (x < 0) x = 0;
            if (y > maxY) y = maxY;
            if (y < 0) y = 0;

            // Set lens position
            lens.style.left = x + 'px';
            lens.style.top = y + 'px';
            lens.style.width = lensWidth + 'px';
            lens.style.height = lensHeight + 'px';

            // Calculate and set background position for result
            const ratio = zoomLevel;
            const bgX = x * ratio;
            const bgY = y * ratio;

            // Set background properties for zoom result
            result.style.backgroundSize = (imgRect.width * ratio) + 'px ' + (imgRect.height * ratio) + 'px';
            result.style.backgroundPosition = '-' + bgX + 'px -' + bgY + 'px';
        }

        // Get cursor position relative to image
        function getCursorPos(e) {
            const rect = img.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            return { x, y };
        }

        // Initialize zoom
        initZoom();
    });
</script>
{% endblock %}