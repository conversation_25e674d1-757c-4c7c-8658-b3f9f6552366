{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Chat History - Mazadi{% endblock %}

{% block body %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-lg mb-6 p-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="bg-blue-500 text-white p-3 rounded-full">
                    <i class="fas fa-history text-xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Chat History</h1>
                    <p class="text-gray-600">Your previous conversations with the smart assistant</p>
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="{% url 'chatbot:chat' %}" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                    <i class="fas fa-comments mr-2"></i>New Chat
                </a>
                <a href="{% url 'index' %}" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
            </div>
        </div>
    </div>

    {% if error %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            {{ error }}
        </div>
    {% endif %}

    <!-- Conversations List -->
    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        {% if conversations %}
            <div class="divide-y divide-gray-200">
                {% for conversation in conversations %}
                    <div class="p-6 hover:bg-gray-50 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="bg-blue-100 text-blue-600 p-3 rounded-full">
                                    <i class="fas fa-comments"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800">
                                        Conversation {{ conversation.session_id|slice:":8" }}...
                                    </h3>
                                    <div class="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                                        <span>
                                            <i class="fas fa-calendar mr-1"></i>
                                            {{ conversation.created_at|date:"M d, Y" }}
                                        </span>
                                        <span>
                                            <i class="fas fa-clock mr-1"></i>
                                            {{ conversation.updated_at|time:"H:i" }}
                                        </span>
                                        <span>
                                            <i class="fas fa-language mr-1"></i>
                                            {{ conversation.get_language_display }}
                                        </span>
                                        <span class="flex items-center">
                                            <i class="fas fa-comment-dots mr-1"></i>
                                            {{ conversation.messages.count }} messages
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                {% if conversation.is_active %}
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                        Active
                                    </span>
                                {% else %}
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                                        Inactive
                                    </span>
                                {% endif %}
                                <a href="{% url 'chatbot:conversation_detail' conversation.id %}"
                                   class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-eye mr-2"></i>View
                                </a>
                            </div>
                        </div>

                        <!-- Preview of last few messages -->
                        {% if conversation.messages.all %}
                            <div class="mt-4 bg-gray-50 rounded-lg p-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Recent Messages:</h4>
                                <div class="space-y-2">
                                    {% for message in conversation.messages.all|slice:":3" %}
                                        <div class="flex items-start space-x-2">
                                            <div class="{% if message.message_type == 'user' %}bg-purple-500{% else %}bg-blue-500{% endif %} text-white p-1 rounded-full flex-shrink-0">
                                                <i class="fas {% if message.message_type == 'user' %}fa-user{% else %}fa-robot{% endif %} text-xs"></i>
                                            </div>
                                            <div class="flex-1">
                                                <p class="text-sm text-gray-800">
                                                    {{ message.content|truncatechars:100 }}
                                                </p>
                                                <span class="text-xs text-gray-500">
                                                    {{ message.timestamp|timesince }} ago
                                                </span>
                                            </div>
                                        </div>
                                    {% endfor %}
                                    {% if conversation.messages.count > 3 %}
                                        <p class="text-xs text-gray-500 text-center">
                                            ... and {{ conversation.messages.count|add:"-3" }} more messages
                                        </p>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if conversations.has_other_pages %}
                <div class="bg-gray-50 px-6 py-4 border-t">
                    <nav class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            {% if conversations.has_previous %}
                                <a href="?page=1" class="bg-white border border-gray-300 text-gray-500 hover:text-gray-700 px-3 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                                <a href="?page={{ conversations.previous_page_number }}" class="bg-white border border-gray-300 text-gray-500 hover:text-gray-700 px-3 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            {% endif %}
                        </div>

                        <span class="text-sm text-gray-700">
                            Page {{ conversations.number }} of {{ conversations.paginator.num_pages }}
                        </span>

                        <div class="flex items-center space-x-2">
                            {% if conversations.has_next %}
                                <a href="?page={{ conversations.next_page_number }}" class="bg-white border border-gray-300 text-gray-500 hover:text-gray-700 px-3 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                                <a href="?page={{ conversations.paginator.num_pages }}" class="bg-white border border-gray-300 text-gray-500 hover:text-gray-700 px-3 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            {% endif %}
                        </div>
                    </nav>
                </div>
            {% endif %}
        {% else %}
            <!-- Empty State -->
            <div class="text-center py-12">
                <div class="bg-gray-100 text-gray-400 p-6 rounded-full w-24 h-24 mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-comments text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">No Chat History</h3>
                <p class="text-gray-600 mb-6">You haven't started any conversations with our smart assistant yet.</p>
                <a href="{% url 'chatbot:chat' %}" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors inline-flex items-center">
                    <i class="fas fa-comments mr-2"></i>
                    Start Your First Chat
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
