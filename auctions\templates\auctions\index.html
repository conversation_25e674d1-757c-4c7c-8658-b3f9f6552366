{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}All Auctions - Mazadi{% endblock %}

{% block extra_head %}
<script>
    // Add smooth scroll behavior
    document.addEventListener('DOMContentLoaded', function() {
        // Add hover animation for cards
        const cards = document.querySelectorAll('.animate-slide-in');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.zIndex = '10';
            });
            card.addEventListener('mouseleave', function() {
                this.style.zIndex = '1';
            });
        });
    });
</script>
{% endblock %}

{% block body %}
    <!-- Auctions Grid -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16 mt-5">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for auction in auctions %}
            <div class="animate-slide-in" style="animation-delay: {{ forloop.counter0 }}00ms">
                <div class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl h-full flex flex-col group">
                    <!-- Image container with enhanced hover effects -->
                    <div class="h-64 overflow-hidden relative">
                        <!-- Category badge -->
                        <div class="absolute top-3 left-3 z-10">
                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-white bg-opacity-90 text-primary-700 shadow-sm transform transition-transform duration-300 group-hover:scale-110">
                                <i class="fas fa-tag mr-1"></i> {{ auction.category }}
                            </span>
                        </div>

                        <!-- Status badge -->
                        <div class="absolute top-3 right-3 z-10">
                            {% if auction.is_close %}
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 shadow-sm transform transition-transform duration-300 group-hover:scale-110">
                                    <i class="fas fa-times-circle mr-1"></i> Closed
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 shadow-sm transform transition-transform duration-300 group-hover:scale-110">
                                    <i class="fas fa-check-circle mr-1"></i> Active
                                </span>
                            {% endif %}
                        </div>

                        <!-- Image with enhanced hover effect -->
                        {% if auction.image %}
                            <img
                                src="{{ auction.image.url }}"
                                class="w-full h-full object-contain transition-all duration-700 ease-in-out transform group-hover:scale-110 filter group-hover:brightness-105"
                                alt="{{ auction.title }}"
                                loading="lazy"
                            >
                        {% elif auction.image_url %}
                            <img
                                src="{{ auction.image_url }}"
                                class="w-full h-full object-contain transition-all duration-700 ease-in-out transform group-hover:scale-110 filter group-hover:brightness-105"
                                alt="{{ auction.title }}"
                                loading="lazy"
                            >
                        {% else %}
                            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-image text-gray-400 text-4xl"></i>
                            </div>
                        {% endif %}

                        <!-- Overlay gradient on hover -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-0 group-hover:opacity-50 transition-opacity duration-300"></div>
                    </div>

                    <!-- Content with enhanced styling -->
                    <div class="p-6 flex flex-col flex-grow">
                        <div class="mb-3">
                            <h3 class="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{ auction.title }}</h3>
                            <p class="text-gray-600 mt-2 line-clamp-2">{{ auction.description }}</p>
                        </div>

                        <div class="mt-auto">
                            <!-- Price with animation -->
                            <div class="flex justify-end mb-4">
                                <div class="bg-primary-50 px-3 py-1 rounded-full">
                                    <span class="text-lg font-bold text-primary-700 group-hover:text-primary-800 transition-colors duration-300 inline-block transform group-hover:scale-110 transition-transform">
                                        EGP{{ auction.price }}
                                    </span>
                                </div>
                            </div>

                            <!-- CTA Button with enhanced hover effect -->
                            <a href="{% url 'auction' auction.id %}" class="relative overflow-hidden block w-full text-center px-4 py-3 border-2 border-primary-600 text-primary-600 rounded-md bg-transparent group-hover:bg-primary-600 group-hover:text-white transition-all duration-300 group-hover:shadow-lg group-hover:border-primary-700 transform group-hover:scale-105">
                                <span class="relative z-10 flex items-center justify-center font-medium">
                                    <span>View Details</span>
                                    <i class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-1"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
                <div class="col-span-full animate-fade-in">
                    <div class="bg-white border border-gray-200 p-8 rounded-lg shadow-md text-center max-w-2xl mx-auto">
                        <div class="mb-6">
                            <div class="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4 transform transition-transform duration-700 hover:rotate-12">
                                <i class="fas fa-gavel text-yellow-500 text-3xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">No Auctions Found</h3>
                            <p class="text-gray-600 max-w-md mx-auto">
                                There are no auctions listed yet. Be the first to create one and start the bidding!
                            </p>
                        </div>

                        {% if user.is_authenticated %}
                            <div class="mt-6">
                                <a href="{% url 'create' %}" class="inline-flex items-center justify-center bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-md shadow-md transition-all duration-300 transform hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                    <i class="fas fa-plus-circle mr-2"></i> Create Your First Auction
                                </a>
                            </div>
                        {% else %}
                            <div class="mt-6 space-y-4">
                                <p class="text-gray-600">Sign in to create your first auction</p>
                                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                                    <a href="{% url 'login' %}" class="inline-flex items-center justify-center bg-white border border-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm transition-all duration-300 hover:bg-gray-50 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                        <i class="fas fa-sign-in-alt mr-2"></i> Log In
                                    </a>
                                    <a href="{% url 'register' %}" class="inline-flex items-center justify-center bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md shadow-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                        <i class="fas fa-user-plus mr-2"></i> Register
                                    </a>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Modern Pagination with Enhanced UI -->
        {% if auctions.paginator.num_pages > 1 %}
            <div class="mt-16 flex flex-col items-center">
                <div class="text-sm text-gray-500 mb-5 bg-gray-50 px-4 py-2 rounded-full shadow-sm">
                    Showing <span class="font-medium text-primary-600">{{ auctions.start_index }}</span> to <span class="font-medium text-primary-600">{{ auctions.end_index }}</span> of <span class="font-medium text-primary-600">{{ auctions.paginator.count }}</span> results
                </div>

                <nav class="flex items-center space-x-2" aria-label="Pagination">
                    <!-- Previous button with modern design -->
                    {% if auctions.has_previous %}
                        <a href="?page={{ auctions.previous_page_number }}" class="relative flex items-center justify-center w-10 h-10 rounded-full bg-white text-gray-600 shadow-md transition-all duration-300 hover:bg-primary-50 hover:text-primary-600 hover:shadow-lg group">
                            <span class="sr-only">Previous</span>
                            <i class="fas fa-chevron-left text-sm transition-transform duration-300 group-hover:-translate-x-0.5"></i>
                        </a>
                    {% else %}
                        <span class="relative flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 text-gray-400 shadow-sm cursor-not-allowed">
                            <span class="sr-only">Previous</span>
                            <i class="fas fa-chevron-left text-sm"></i>
                        </span>
                    {% endif %}

                    <!-- Page numbers with modern design -->
                    {% for i in auctions.paginator.page_range %}
                        {% if auctions.number == i %}
                            <!-- Current page -->
                            <span class="relative flex items-center justify-center w-10 h-10 rounded-full bg-primary-600 text-white font-medium shadow-lg transform scale-110 transition-all duration-300">
                                {{ i }}
                            </span>
                        {% elif i > auctions.number|add:"-3" and i < auctions.number|add:"3" %}
                            <!-- Pages near current page -->
                            <a href="?page={{ i }}" class="relative flex items-center justify-center w-10 h-10 rounded-full bg-white text-gray-700 font-medium shadow-md transition-all duration-300 hover:bg-primary-50 hover:text-primary-600 hover:shadow-lg hover:scale-105">
                                {{ i }}
                            </a>
                        {% elif i == 1 or i == auctions.paginator.num_pages %}
                            <!-- First and last page -->
                            <a href="?page={{ i }}" class="relative flex items-center justify-center w-10 h-10 rounded-full bg-white text-gray-700 font-medium shadow-md transition-all duration-300 hover:bg-primary-50 hover:text-primary-600 hover:shadow-lg hover:scale-105">
                                {{ i }}
                            </a>
                        {% elif i == auctions.number|add:"-3" or i == auctions.number|add:"3" %}
                            <!-- Ellipsis for page gaps -->
                            <span class="relative flex items-center justify-center w-8 text-gray-400">
                                <i class="fas fa-ellipsis-h"></i>
                            </span>
                        {% endif %}
                    {% endfor %}

                    <!-- Next button with modern design -->
                    {% if auctions.has_next %}
                        <a href="?page={{ auctions.next_page_number }}" class="relative flex items-center justify-center w-10 h-10 rounded-full bg-white text-gray-600 shadow-md transition-all duration-300 hover:bg-primary-50 hover:text-primary-600 hover:shadow-lg group">
                            <span class="sr-only">Next</span>
                            <i class="fas fa-chevron-right text-sm transition-transform duration-300 group-hover:translate-x-0.5"></i>
                        </a>
                    {% else %}
                        <span class="relative flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 text-gray-400 shadow-sm cursor-not-allowed">
                            <span class="sr-only">Next</span>
                            <i class="fas fa-chevron-right text-sm"></i>
                        </span>
                    {% endif %}
                </nav>

                <!-- Jump to page (for many pages) -->
                {% if auctions.paginator.num_pages > 5 %}
                    <div class="mt-6 flex items-center space-x-3">
                        <span class="text-sm text-gray-500">Jump to page:</span>
                        <form method="get" class="flex items-center">
                            <input
                                type="number"
                                name="page"
                                min="1"
                                max="{{ auctions.paginator.num_pages }}"
                                value="{{ auctions.number }}"
                                class="w-16 h-9 rounded-l-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50 text-center"
                            >
                            <button type="submit" class="h-9 px-3 rounded-r-md bg-primary-600 text-white shadow-sm hover:bg-primary-700 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50">
                                <i class="fas fa-arrow-right text-xs"></i>
                            </button>
                        </form>
                    </div>
                {% endif %}
            </div>
        {% endif %}
    </div>
{% endblock %}