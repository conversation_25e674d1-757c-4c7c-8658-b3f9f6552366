{% if ratings %}
    <div class="space-y-6">
        {% for rating in ratings %}
            <div class="bg-white shadow-sm rounded-lg overflow-hidden transition-all duration-300 hover:shadow-md">
                <div class="p-6">
                    <div class="flex items-start">
                        <!-- User Avatar -->
                        <div class="flex-shrink-0">
                            {% if rating.rater.profile.profile_picture %}
                                <img src="{{ rating.rater.profile.profile_picture.url }}" alt="{{ rating.rater.username }}" class="h-12 w-12 rounded-full object-contain">
                            {% else %}
                                <div class="h-12 w-12 rounded-full bg-primary-100 flex items-center justify-center">
                                    <i class="fas fa-user text-lg text-primary-500"></i>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Rating Content -->
                        <div class="ml-4 flex-1">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">
                                        <a href="{% url 'public_profile' rating.rater.username %}" class="hover:text-primary-600 transition-colors duration-300">
                                            {{ rating.rater.username }}
                                        </a>
                                        <span class="text-sm text-gray-500 ml-2">
                                            rated as
                                            {% if rating.as_seller %}
                                                <span class="text-green-600 font-medium">Seller</span>
                                            {% endif %}
                                            {% if rating.as_seller and rating.as_buyer %}
                                                and
                                            {% endif %}
                                            {% if rating.as_buyer %}
                                                <span class="text-blue-600 font-medium">Buyer</span>
                                            {% endif %}
                                        </span>
                                    </h3>
                                    <div class="flex items-center mt-1">
                                        <div class="flex items-center">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= rating.score %}
                                                    <i class="fas fa-star text-yellow-500"></i>
                                                {% else %}
                                                    <i class="far fa-star text-yellow-500"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                        <span class="ml-2 text-sm text-gray-500">{{ rating.created_at|date:"F j, Y" }}</span>
                                    </div>
                                </div>

                                {% if rating.auction %}
                                <div>
                                    <a href="{% url 'auction' rating.auction.id %}" class="text-sm text-primary-600 hover:text-primary-800 transition-colors duration-300">
                                        <i class="fas fa-gavel mr-1"></i>
                                        View Auction
                                    </a>
                                </div>
                                {% endif %}
                            </div>

                            {% if rating.comment %}
                            <div class="mt-3 text-gray-700">
                                {{ rating.comment }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-12">
        <div class="mx-auto h-24 w-24 text-gray-400">
            <i class="fas fa-star-half-alt text-6xl"></i>
        </div>
        <h3 class="mt-2 text-lg font-medium text-gray-900">No ratings yet</h3>
        <p class="mt-1 text-gray-500">This user hasn't received any ratings in this category yet.</p>
    </div>
{% endif %}
