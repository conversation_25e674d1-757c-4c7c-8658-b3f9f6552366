{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Your Watchlist - Mazadi{% endblock %}

{% block extra_head %}
<script>
    // Add hover animation for cards
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.animate-slide-in');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.zIndex = '10';
            });
            card.addEventListener('mouseleave', function() {
                this.style.zIndex = '1';
            });
        });
    });
</script>
{% endblock %}

{% block body %}

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Watchlist Stats -->
    <div class="bg-white shadow-sm rounded-lg p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div>
                <h3 class="text-lg font-medium text-gray-500">Total Items</h3>
                <p class="mt-2 text-3xl font-bold text-primary-600">{{ watchlists|length }}</p>
            </div>
            <div>
                <h3 class="text-lg font-medium text-gray-500">Active Auctions</h3>
                <p class="mt-2 text-3xl font-bold text-green-600">
                    {% with active_count=0 %}
                        {% for auction in watchlists %}
                            {% if not auction.is_close %}
                                {% with active_count=active_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ active_count }}
                    {% endwith %}
                </p>
            </div>
            <div>
                <h3 class="text-lg font-medium text-gray-500">Closed Auctions</h3>
                <p class="mt-2 text-3xl font-bold text-red-600">
                    {% with closed_count=0 %}
                        {% for auction in watchlists %}
                            {% if auction.is_close %}
                                {% with closed_count=closed_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ closed_count }}
                    {% endwith %}
                </p>
            </div>
        </div>
    </div>

    <!-- Watchlist Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        {% for auction in watchlists %}
        <div class="animate-slide-in" style="animation-delay: {{ forloop.counter0 }}00ms">
            <div class="bg-white rounded-lg shadow-md overflow-hidden transition-all duration-500 transform hover:-translate-y-2 hover:shadow-xl h-full flex flex-col group">
                <!-- Image container with enhanced hover effects -->
                <div class="h-64 overflow-hidden relative">
                    <!-- Remove from Watchlist Button -->
                    <div class="absolute top-3 left-3 z-20">
                        <form action="{% url 'remove' %}" method="post" class="inline">
                            {% csrf_token %}
                            <input type="hidden" name="auction_id" value="{{ auction.id }}">
                            <button type="submit" class="bg-white rounded-full p-2 shadow-sm hover:bg-red-50 transition-all duration-300 transform hover:scale-110 hover:rotate-12" title="Remove from Watchlist">
                                <i class="fas fa-trash-alt text-red-500"></i>
                            </button>
                        </form>
                    </div>

                    <!-- Category badge -->
                    <div class="absolute top-3 right-3 z-10">
                        <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-white bg-opacity-90 text-primary-700 shadow-sm transform transition-transform duration-300 group-hover:scale-110">
                            <i class="fas fa-tag mr-1"></i> {{ auction.category }}
                        </span>
                    </div>

                    <!-- Status badge -->
                    <div class="absolute bottom-3 left-3 z-10">
                        {% if auction.is_close %}
                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 shadow-sm transform transition-transform duration-300 group-hover:scale-110">
                                <i class="fas fa-times-circle mr-1"></i> Closed
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 shadow-sm transform transition-transform duration-300 group-hover:scale-110">
                                <i class="fas fa-check-circle mr-1"></i> Active
                            </span>
                        {% endif %}
                    </div>

                    <!-- Image with enhanced hover effect -->
                    {% if auction.image %}
                        <img
                            src="{{ auction.image.url }}"
                            class="w-full h-full object-contain transition-all duration-700 ease-in-out transform group-hover:scale-110 filter group-hover:brightness-105"
                            alt="{{ auction.title }}"
                            loading="lazy"
                        >
                    {% elif auction.image_url %}
                        <img
                            src="{{ auction.image_url }}"
                            class="w-full h-full object-contain transition-all duration-700 ease-in-out transform group-hover:scale-110 filter group-hover:brightness-105"
                            alt="{{ auction.title }}"
                            loading="lazy"
                        >
                    {% else %}
                        <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                            <i class="fas fa-image text-gray-400 text-4xl"></i>
                        </div>
                    {% endif %}

                    <!-- Overlay gradient on hover -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-0 group-hover:opacity-50 transition-opacity duration-300"></div>
                </div>

                <!-- Content with enhanced styling -->
                <div class="p-6 flex flex-col flex-grow">
                    <div class="mb-3">
                        <h3 class="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">{{ auction.title }}</h3>
                        <p class="text-gray-600 mt-2 line-clamp-2">{{ auction.description }}</p>
                    </div>

                    <div class="mt-auto">
                        <!-- Price with animation -->
                        <div class="flex justify-end mb-4">
                            <div class="bg-primary-50 px-3 py-1 rounded-full">
                                <span class="text-lg font-bold text-primary-700 group-hover:text-primary-800 transition-colors duration-300 inline-block transform group-hover:scale-110 transition-transform">
                                    EGP{{ auction.price }}
                                </span>
                            </div>
                        </div>

                        <!-- CTA Button with enhanced hover effect -->
                        <a href="{% url 'auction' auction.id %}" class="relative overflow-hidden block w-full text-center px-4 py-3 border-2 border-primary-600 text-primary-600 rounded-md bg-transparent group-hover:bg-primary-600 group-hover:text-white transition-all duration-300 group-hover:shadow-lg group-hover:border-primary-700 transform group-hover:scale-105">
                            <span class="relative z-10 flex items-center justify-center font-medium">
                                <span>View Details</span>
                                <i class="fas fa-arrow-right ml-2 transform transition-transform duration-300 group-hover:translate-x-1"></i>
                            </span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
            <div class="col-span-full animate-fade-in">
                <div class="bg-white border border-gray-200 p-8 rounded-lg shadow-md text-center max-w-2xl mx-auto">
                    <div class="mb-6">
                        <div class="w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mx-auto mb-4 transform transition-transform duration-700 hover:rotate-12">
                            <i class="fas fa-heart-broken text-red-400 text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Your Watchlist is Empty</h3>
                        <p class="text-gray-600 max-w-md mx-auto">
                            You haven't added any auctions to your watchlist yet. Browse auctions and click "Add to Watchlist" to keep track of items you're interested in.
                        </p>
                    </div>

                    <div class="mt-6">
                        <a href="{% url 'index' %}" class="inline-flex items-center justify-center bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-md shadow-md transition-all duration-300 transform hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-search mr-2"></i> Browse Auctions
                        </a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>
{% endblock %}