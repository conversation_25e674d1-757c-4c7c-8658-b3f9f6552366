{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}{{ title }} - <PERSON><PERSON><PERSON>{% endblock %}

{% block body %}
<div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Conversation with {{ other_user.username }}</h1>
            <p class="text-gray-600">Regarding auction: <a href="{% url 'auction' auction.id %}" class="text-primary-600 hover:underline">{{ auction.title }}</a></p>
        </div>
        <div class="flex space-x-4">
            <a href="{% url 'payment_receipt' payment.id %}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-receipt mr-2"></i> View Receipt
            </a>
            <a href="{% url 'messages_inbox' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-inbox mr-2"></i> Back to Inbox
            </a>
        </div>
    </div>

    <!-- Message Thread -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    {% if other_user.profile.profile_picture %}
                        <img src="{{ other_user.profile.profile_picture.url }}" alt="{{ other_user.username }}" class="h-10 w-10 rounded-full object-cover border border-primary-300">
                    {% else %}
                        <div class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                            <i class="fas fa-user text-primary-500"></i>
                        </div>
                    {% endif %}
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">{{ other_user.username }}</h3>
                    <p class="text-sm text-gray-500">{{ other_user.email }}</p>
                </div>
            </div>
        </div>

        <div class="p-6 max-h-96 overflow-y-auto" id="message-container">
            {% if thread_messages %}
                <div class="space-y-4">
                    {% for msg in thread_messages %}
                        <div class="flex {% if msg.sender == request.user %}justify-end{% endif %}">
                            <div class="max-w-md {% if msg.sender == request.user %}bg-primary-100 text-primary-800{% else %}bg-gray-100 text-gray-800{% endif %} rounded-lg px-4 py-3 shadow-sm">
                                <div class="flex items-center mb-1">
                                    <span class="font-medium text-sm">{{ msg.sender.username }}</span>
                                    <span class="text-xs text-gray-500 ml-2">{{ msg.created_at|date:"M d, Y" }} at {{ msg.created_at|time:"g:i A" }}</span>
                                </div>
                                <p class="text-sm whitespace-pre-wrap">{{ msg.content }}</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
                        <i class="far fa-comment-dots text-gray-400 text-2xl"></i>
                    </div>
                    <p class="text-gray-500 italic">No messages yet. Start the conversation!</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Message Form -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <form method="post" action="{% url 'messages_thread' payment.id %}" class="p-6">
            {% csrf_token %}
            <div class="mb-4">
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">Send a message</label>
                <textarea id="content" name="content" rows="3" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="Type your message here..."></textarea>
            </div>
            <div class="flex justify-end">
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-paper-plane mr-2"></i> Send Message
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Scroll to bottom of message container
        const messageContainer = document.getElementById('message-container');
        messageContainer.scrollTop = messageContainer.scrollHeight;
        
        // Focus on message input
        document.getElementById('content').focus();
    });
</script>
{% endblock %}
