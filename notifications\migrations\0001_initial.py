# Generated by Django 5.2.1 on 2025-05-19 00:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_bid', models.BooleanField(default=True)),
                ('email_outbid', models.BooleanField(default=True)),
                ('email_auction_won', models.BooleanField(default=True)),
                ('email_auction_ended', models.BooleanField(default=True)),
                ('email_comment', models.BooleanField(default=False)),
                ('email_rating', models.BooleanField(default=True)),
                ('email_message', models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                ('email_system', models.<PERSON><PERSON>anField(default=True)),
                ('app_bid', models.BooleanField(default=True)),
                ('app_outbid', models.BooleanField(default=True)),
                ('app_auction_won', models.BooleanField(default=True)),
                ('app_auction_ended', models.BooleanField(default=True)),
                ('app_comment', models.BooleanField(default=True)),
                ('app_rating', models.BooleanField(default=True)),
                ('app_message', models.BooleanField(default=True)),
                ('app_system', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('message', models.TextField()),
                ('notification_type', models.CharField(choices=[('bid', 'New Bid'), ('outbid', 'Outbid'), ('auction_won', 'Auction Won'), ('auction_ended', 'Auction Ended'), ('comment', 'New Comment'), ('rating', 'New Rating'), ('message', 'New Message'), ('system', 'System Notification')], max_length=20)),
                ('level', models.CharField(choices=[('info', 'Information'), ('success', 'Success'), ('warning', 'Warning'), ('error', 'Error')], default='info', max_length=10)),
                ('link', models.CharField(blank=True, max_length=255, null=True)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('is_read', models.BooleanField(default=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'is_read', 'created_at'], name='notificatio_user_id_8a7c6b_idx'), models.Index(fields=['notification_type'], name='notificatio_notific_f2898f_idx')],
            },
        ),
    ]
