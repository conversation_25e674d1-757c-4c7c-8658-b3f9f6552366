{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Seller Payments - Ma<PERSON>i{% endblock %}

{% block body %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Seller Payment Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-secondary-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-secondary-100 mr-4">
                    <i class="fas fa-receipt text-secondary-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500 uppercase">Total Payments</p>
                    <p class="text-2xl font-bold text-gray-900">{{ payment_count }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 mr-4">
                    <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500 uppercase">Total Earnings</p>
                    <p class="text-2xl font-bold text-gray-900">${{ total_earnings }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 mr-4">
                    <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500 uppercase">Avg. Payment</p>
                    <p class="text-2xl font-bold text-gray-900">
                        {% if payment_count > 0 %}
                            ${{ total_earnings|floatformat:2 }}
                        {% else %}
                            $0.00
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Seller Payments Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Payments Received</h2>
        </div>

        {% if payments %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Auction
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Buyer
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for payment in payments %}
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-md overflow-hidden">
                                            {% if payment.auction.image %}
                                                <img src="{{ payment.auction.image.url }}" alt="{{ payment.auction.title }}" class="h-10 w-10 object-contain">
                                            {% elif payment.auction.image_url %}
                                                <img src="{{ payment.auction.image_url }}" alt="{{ payment.auction.title }}" class="h-10 w-10 object-contain">
                                            {% else %}
                                                <div class="h-10 w-10 flex items-center justify-center">
                                                    <i class="fas fa-image text-gray-400"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 truncate max-w-xs">
                                                {{ payment.auction.title }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                ID: #{{ payment.auction.id }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8">
                                            {% if payment.user.profile.profile_picture %}
                                                <img src="{{ payment.user.profile.profile_picture.url }}" alt="{{ payment.user.username }}" class="h-8 w-8 rounded-full object-cover">
                                            {% else %}
                                                <div class="h-8 w-8 rounded-full bg-secondary-100 flex items-center justify-center">
                                                    <i class="fas fa-user text-secondary-500 text-sm"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="ml-3">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ payment.user.username }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">${{ payment.amount }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ payment.created_at|date:"F j, Y" }}</div>
                                    <div class="text-sm text-gray-500">{{ payment.created_at|time:"g:i A" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if payment.status == 'completed' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            Completed
                                        </span>
                                    {% elif payment.status == 'pending' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            Pending
                                        </span>
                                    {% elif payment.status == 'failed' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                            Failed
                                        </span>
                                    {% elif payment.status == 'refunded' %}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            Refunded
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{% url 'auction' payment.auction.id %}" class="text-secondary-600 hover:text-secondary-900 mr-3">
                                        <i class="fas fa-eye"></i> View Auction
                                    </a>
                                    {% if payment.status == 'completed' %}
                                        <a href="{% url 'payment_receipt' payment.id %}" class="text-gray-600 hover:text-gray-900">
                                            <i class="fas fa-receipt"></i> Receipt
                                        </a>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="p-12 text-center">
                <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4">
                    <i class="fas fa-receipt text-gray-400 text-2xl"></i>
                </div>
                <p class="text-gray-500 mb-4">You haven't received any payments yet.</p>
                <a href="{% url 'create' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary-600 hover:bg-secondary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary-500">
                    <i class="fas fa-plus mr-2"></i> Create an Auction
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
