{% extends "auctions/layout.html" %}
{% load static %}

{% block title %}Payment - Ma<PERSON>i{% endblock %}

{% block body %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <!-- Payment Summary -->
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Order Summary</h2>
            
            <div class="flex items-start space-x-4">
                <!-- Auction Image -->
                <div class="flex-shrink-0 w-24 h-24 bg-gray-100 rounded-md overflow-hidden">
                    {% if auction.image %}
                        <img src="{{ auction.image.url }}" alt="{{ auction.title }}" class="w-full h-full object-contain">
                    {% elif auction.image_url %}
                        <img src="{{ auction.image_url }}" alt="{{ auction.title }}" class="w-full h-full object-contain">
                    {% else %}
                        <div class="w-full h-full flex items-center justify-center">
                            <i class="fas fa-image text-gray-400 text-2xl"></i>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Auction Details -->
                <div class="flex-1">
                    <h3 class="text-lg font-medium text-gray-900">{{ auction.title }}</h3>
                    <p class="mt-1 text-sm text-gray-500">{{ auction.category }}</p>
                    <div class="mt-2 flex items-center">
                        <span class="text-sm text-gray-500">Sold by:</span>
                        <span class="ml-1 text-sm font-medium text-gray-900">{{ auction.user.username }}</span>
                    </div>
                </div>
                
                <!-- Price -->
                <div class="flex-shrink-0 text-right">
                    <p class="text-lg font-medium text-gray-900">${{ payment.amount }}</p>
                    <p class="mt-1 text-sm text-gray-500">Final Bid</p>
                </div>
            </div>
        </div>
        
        <!-- Payment Form -->
        <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Payment Details</h2>
            
            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            This is a test payment. No real money will be charged. Use the test card number below.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-credit-card text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            <strong>Test Card:</strong> 4242 4242 4242 4242 | Exp: Any future date | CVC: Any 3 digits
                        </p>
                    </div>
                </div>
            </div>
            
            <form id="payment-form" class="space-y-6">
                <div id="payment-element">
                    <!-- Stripe Elements will be inserted here -->
                </div>
                
                <div id="payment-message" class="hidden bg-red-50 border-l-4 border-red-400 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-700" id="payment-message-text"></p>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-between items-center">
                    <a href="{% url 'auction' auction.id %}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Auction
                    </a>
                    
                    <button id="submit-button" type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-lock mr-2"></i>
                        Pay ${{ payment.amount }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://js.stripe.com/v3/"></script>
<script>
    // Initialize Stripe
    const stripe = Stripe('{{ stripe_publishable_key }}');
    
    // Create payment elements
    const elements = stripe.elements({
        clientSecret: '{{ client_secret }}'
    });
    
    // Create and mount the Payment Element
    const paymentElement = elements.create('payment');
    paymentElement.mount('#payment-element');
    
    // Handle form submission
    const form = document.getElementById('payment-form');
    const submitButton = document.getElementById('submit-button');
    const paymentMessage = document.getElementById('payment-message');
    const paymentMessageText = document.getElementById('payment-message-text');
    
    form.addEventListener('submit', async (event) => {
        event.preventDefault();
        
        // Disable the submit button to prevent multiple clicks
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';
        
        // Confirm the payment
        const {error} = await stripe.confirmPayment({
            elements,
            confirmParams: {
                return_url: window.location.origin + "{% url 'payment_success' payment.id %}",
            },
        });
        
        if (error) {
            // Show error message
            paymentMessageText.textContent = error.message;
            paymentMessage.classList.remove('hidden');
            
            // Re-enable the submit button
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-lock mr-2"></i> Pay ${{ payment.amount }}';
        }
    });
</script>
{% endblock %}
