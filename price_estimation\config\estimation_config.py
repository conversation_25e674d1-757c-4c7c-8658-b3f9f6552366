"""
Configuration data for price estimation.

Contains brands, categories, price ranges, and other configuration data.
"""


class EstimationConfig:
    """Configuration class for price estimation data."""
    
    # Common electronics brands in Egyptian market
    BRANDS = [
        'samsung', 'apple', 'dell', 'hp', 'lenovo', 'asus', 'acer', 'sony', 'lg',
        'huawei', 'xiaomi', 'oppo', 'vivo', 'nokia', 'intel', 'amd', 'nvidia',
        'microsoft', 'google', 'canon', 'nikon', 'bose', 'jbl', 'beats',
        'logitech', 'razer', 'corsair', 'steelseries', 'hyperx'
    ]
    
    # Product categories with keywords
    CATEGORIES = {
        'laptop': ['laptop', 'notebook', 'macbook'],
        'phone': ['phone', 'iphone', 'smartphone', 'mobile'],
        'tablet': ['tablet', 'ipad'],
        'monitor': ['monitor', 'screen', 'display', 'شاشة'],
        'keyboard': ['keyboard', 'كيبورد'],
        'mouse': ['mouse', 'ماوس'],
        'headphones': ['headphones', 'earphones', 'headset'],
        'speaker': ['speaker', 'speakers'],
        'camera': ['camera', 'كاميرا'],
        'tv': ['tv', 'television', 'smart tv']
    }

    # Constants
    USED_DISCOUNT_FACTOR = 0.8
    SIMILARITY_THRESHOLD = 0.35  # Lowered to capture more relevant listings
    MAX_SEARCH_LENGTH = 50
    CACHE_TIMEOUT = 3600  # 1 hour in seconds
